"""
Finance Domain Models for EMS
Contains financial management related models:
- Currency: Multi-currency support
- ExchangeRate: Currency exchange rates
- AccountType: Chart of accounts types
- ChartOfAccounts: General ledger accounts
- FiscalYear: Fiscal year management
- JournalEntryBatch: Journal entry batches
- JournalEntry: General ledger entries
- Budget: Budget management
- Expense: Expense tracking
- Vendor: Vendor/supplier management
- VendorInvoice: Accounts payable
- CustomerInvoice: Accounts receivable
- Payment: Payment processing
"""

from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator
from decimal import Decimal
from .base import BaseModel, NamedModel, AuditableModel, MoneyField, APPROVAL_STATUS_CHOICES


class Currency(BaseModel):
    """
    Currency model for multi-currency support
    """
    SYMBOL_POSITION_CHOICES = [
        ('before', 'Before amount (e.g., $100)'),
        ('after', 'After amount (e.g., 100$)'),
    ]

    code = models.CharField(
        max_length=3,
        unique=True,
        help_text="ISO 4217 currency code (e.g., USD, EUR, SAR)"
    )
    name = models.CharField(max_length=100, help_text="Full currency name")
    symbol = models.CharField(max_length=10, help_text="Currency symbol (e.g., $, €, ﷼)")
    decimal_places = models.PositiveIntegerField(
        default=2,
        help_text="Number of decimal places"
    )
    is_base_currency = models.BooleanField(
        default=False,
        help_text="Is this the base currency for the system?"
    )
    is_active = models.BooleanField(default=True, help_text="Is this currency active?")

    # Display settings
    symbol_position = models.CharField(
        max_length=10,
        choices=SYMBOL_POSITION_CHOICES,
        default='before'
    )

    def __str__(self):
        return f"{self.code} - {self.name}"

    def save(self, *args, **kwargs):
        # Ensure only one base currency exists
        if self.is_base_currency:
            Currency.objects.filter(is_base_currency=True).exclude(pk=self.pk).update(is_base_currency=False)
        super().save(*args, **kwargs)

    def format_amount(self, amount):
        """Format amount with currency symbol"""
        formatted_amount = f"{amount:.{self.decimal_places}f}"
        if self.symbol_position == 'before':
            return f"{self.symbol}{formatted_amount}"
        else:
            return f"{formatted_amount}{self.symbol}"

    class Meta:
        verbose_name_plural = "Currencies"
        ordering = ['code']


class ExchangeRate(BaseModel):
    """
    Exchange rate model for currency conversions
    """
    SOURCE_CHOICES = [
        ('manual', 'Manual Entry'),
        ('api', 'API Feed'),
        ('bank', 'Bank Rate'),
        ('central_bank', 'Central Bank Rate'),
    ]

    from_currency = models.ForeignKey(
        Currency,
        on_delete=models.CASCADE,
        related_name='rates_from'
    )
    to_currency = models.ForeignKey(
        Currency,
        on_delete=models.CASCADE,
        related_name='rates_to'
    )
    rate = models.DecimalField(
        max_digits=15,
        decimal_places=6,
        help_text="Exchange rate from source to target currency"
    )
    effective_date = models.DateField(help_text="Date when this rate becomes effective")
    source = models.CharField(
        max_length=50,
        choices=SOURCE_CHOICES,
        default='manual'
    )
    is_active = models.BooleanField(default=True)
    created_by = models.ForeignKey(
        'core.Employee',
        on_delete=models.SET_NULL,
        null=True,
        blank=True
    )

    def __str__(self):
        return f"{self.from_currency.code} to {self.to_currency.code}: {self.rate} ({self.effective_date})"

    @classmethod
    def get_rate(cls, from_currency, to_currency, date=None):
        """Get the most recent exchange rate for the given currencies and date"""
        if date is None:
            date = timezone.now().date()

        # If same currency, return 1
        if from_currency == to_currency:
            return 1

        # Try direct rate
        try:
            rate = cls.objects.filter(
                from_currency=from_currency,
                to_currency=to_currency,
                effective_date__lte=date,
                is_active=True
            ).order_by('-effective_date').first()

            if rate:
                return rate.rate
        except cls.DoesNotExist:
            pass

        # Try inverse rate
        try:
            rate = cls.objects.filter(
                from_currency=to_currency,
                to_currency=from_currency,
                effective_date__lte=date,
                is_active=True
            ).order_by('-effective_date').first()

            if rate and rate.rate != 0:
                return 1 / rate.rate
        except cls.DoesNotExist:
            pass

        # If no rate found, raise exception
        raise ValueError(f"No exchange rate found for {from_currency.code} to {to_currency.code} on {date}")

    class Meta:
        unique_together = ['from_currency', 'to_currency', 'effective_date']
        ordering = ['-effective_date', 'from_currency__code', 'to_currency__code']
        indexes = [
            models.Index(fields=['from_currency', 'to_currency', '-effective_date']),
            models.Index(fields=['effective_date', 'is_active']),
        ]


class AccountType(BaseModel):
    """
    Account types for Chart of Accounts
    """
    ACCOUNT_TYPES = [
        ('ASSET', 'Asset'),
        ('LIABILITY', 'Liability'),
        ('EQUITY', 'Equity'),
        ('REVENUE', 'Revenue'),
        ('EXPENSE', 'Expense'),
        ('COST_OF_GOODS_SOLD', 'Cost of Goods Sold'),
    ]

    name = models.CharField(max_length=50, choices=ACCOUNT_TYPES, unique=True)
    name_ar = models.CharField(max_length=50, help_text="Arabic name")
    description = models.TextField(blank=True)
    description_ar = models.TextField(blank=True, help_text="Arabic description")
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return self.get_name_display()

    class Meta:
        ordering = ['name']


class ChartOfAccounts(BaseModel):
    """
    Chart of Accounts - Account structure
    """
    account_code = models.CharField(
        max_length=20,
        unique=True,
        help_text="Account code (e.g., 1000, 1100)"
    )
    account_name = models.CharField(max_length=200, help_text="Account name")
    account_name_ar = models.CharField(max_length=200, help_text="Arabic account name")
    account_type = models.ForeignKey(
        AccountType,
        on_delete=models.CASCADE,
        related_name='accounts'
    )
    parent_account = models.ForeignKey(
        'self',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='sub_accounts'
    )
    description = models.TextField(blank=True)
    description_ar = models.TextField(blank=True, help_text="Arabic description")
    is_active = models.BooleanField(default=True)
    is_system_account = models.BooleanField(
        default=False,
        help_text="System-generated account"
    )
    level = models.IntegerField(default=0, help_text="Account hierarchy level")

    # Financial settings
    allow_manual_entries = models.BooleanField(default=True)
    require_department = models.BooleanField(default=False)
    require_project = models.BooleanField(default=False)

    def __str__(self):
        return f"{self.account_code} - {self.account_name}"

    def get_full_code(self):
        """Get full hierarchical account code"""
        if self.parent_account:
            return f"{self.parent_account.get_full_code()}.{self.account_code}"
        return self.account_code

    def get_balance(self, as_of_date=None):
        """Calculate account balance as of specific date"""
        from django.db.models import Sum
        if as_of_date is None:
            as_of_date = timezone.now().date()

        entries = JournalEntry.objects.filter(
            account=self,
            transaction_date__lte=as_of_date,
            is_posted=True
        )

        debit_total = entries.aggregate(Sum('debit_amount'))['debit_amount__sum'] or 0
        credit_total = entries.aggregate(Sum('credit_amount'))['credit_amount__sum'] or 0

        # Asset, Expense accounts: Debit increases balance
        if self.account_type.name in ['ASSET', 'EXPENSE', 'COST_OF_GOODS_SOLD']:
            return debit_total - credit_total
        # Liability, Equity, Revenue accounts: Credit increases balance
        else:
            return credit_total - debit_total

    class Meta:
        ordering = ['account_code']
        verbose_name_plural = "Chart of Accounts"
        indexes = [
            models.Index(fields=['account_code']),
            models.Index(fields=['account_type', 'is_active']),
            models.Index(fields=['parent_account', 'level']),
        ]


class FiscalYear(BaseModel):
    """
    Fiscal Year management
    """
    name = models.CharField(max_length=50, help_text="Fiscal year name")
    start_date = models.DateField(help_text="Fiscal year start date")
    end_date = models.DateField(help_text="Fiscal year end date")
    is_current = models.BooleanField(default=False, help_text="Is this the current fiscal year?")
    is_closed = models.BooleanField(default=False, help_text="Is this fiscal year closed?")

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if self.is_current:
            # Ensure only one fiscal year is current
            FiscalYear.objects.filter(is_current=True).update(is_current=False)
        super().save(*args, **kwargs)

    class Meta:
        ordering = ['-start_date']
        indexes = [
            models.Index(fields=['-start_date']),
            models.Index(fields=['is_current', 'is_closed']),
        ]


class JournalEntryBatch(BaseModel):
    """
    Batch for grouping journal entries
    """
    batch_number = models.CharField(max_length=50, unique=True)
    description = models.CharField(max_length=200)
    description_ar = models.CharField(max_length=200, blank=True, help_text="Arabic description")
    created_by = models.ForeignKey(
        'core.Employee',
        on_delete=models.CASCADE,
        related_name='created_journal_batches'
    )
    is_posted = models.BooleanField(default=False, help_text="Has this batch been posted?")
    posted_at = models.DateTimeField(null=True, blank=True)
    posted_by = models.ForeignKey(
        'core.Employee',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='posted_batches'
    )

    def __str__(self):
        return f"{self.batch_number} - {self.description}"

    def get_total_debits(self):
        """Get total debit amount for this batch"""
        from django.db.models import Sum
        return self.journal_entries.aggregate(Sum('debit_amount'))['debit_amount__sum'] or 0

    def get_total_credits(self):
        """Get total credit amount for this batch"""
        from django.db.models import Sum
        return self.journal_entries.aggregate(Sum('credit_amount'))['credit_amount__sum'] or 0

    def is_balanced(self):
        """Check if batch is balanced (debits = credits)"""
        return self.get_total_debits() == self.get_total_credits()

    def post(self, posted_by):
        """Post the journal entry batch"""
        if self.is_balanced():
            self.is_posted = True
            self.posted_at = timezone.now()
            self.posted_by = posted_by
            self.save()

            # Post all entries in the batch
            self.journal_entries.update(
                is_posted=True,
                posted_at=timezone.now()
            )
        else:
            raise ValueError("Cannot post unbalanced journal entry batch")

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['batch_number']),
            models.Index(fields=['is_posted', '-created_at']),
        ]


class JournalEntry(BaseModel):
    """
    General Ledger Journal Entries
    """
    batch = models.ForeignKey(
        JournalEntryBatch,
        on_delete=models.CASCADE,
        related_name='journal_entries'
    )
    entry_number = models.CharField(max_length=50, help_text="Entry number within batch")
    account = models.ForeignKey(
        ChartOfAccounts,
        on_delete=models.CASCADE,
        related_name='journal_entries'
    )
    transaction_date = models.DateField(help_text="Transaction date")
    description = models.CharField(max_length=200, help_text="Entry description")
    description_ar = models.CharField(max_length=200, blank=True, help_text="Arabic description")

    # Amounts
    debit_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        help_text="Debit amount"
    )
    credit_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        help_text="Credit amount"
    )

    # Optional references
    department = models.ForeignKey(
        'core.Department',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='journal_entries'
    )
    project = models.ForeignKey(
        'projects.Project',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='journal_entries'
    )
    employee = models.ForeignKey(
        'core.Employee',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='journal_entries'
    )

    # Reference to source document
    source_document_type = models.CharField(
        max_length=50,
        blank=True,
        help_text="e.g., Invoice, Payment, Expense"
    )
    source_document_id = models.CharField(max_length=50, blank=True)

    # Status
    is_posted = models.BooleanField(default=False, help_text="Has this entry been posted?")
    posted_at = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        amount = self.debit_amount or self.credit_amount
        return f"{self.entry_number} - {self.account.account_code} - {amount}"

    def clean(self):
        """Validate journal entry"""
        from django.core.exceptions import ValidationError
        if self.debit_amount and self.credit_amount:
            raise ValidationError("Entry cannot have both debit and credit amounts")
        if not self.debit_amount and not self.credit_amount:
            raise ValidationError("Entry must have either debit or credit amount")

    class Meta:
        ordering = ['-transaction_date', 'entry_number']
        verbose_name_plural = "Journal Entries"
        indexes = [
            models.Index(fields=['batch', 'entry_number']),
            models.Index(fields=['account', '-transaction_date']),
            models.Index(fields=['transaction_date', 'is_posted']),
            models.Index(fields=['source_document_type', 'source_document_id']),
        ]


class Budget(BaseModel):
    """
    Budget management for departments and projects
    """
    name = models.CharField(max_length=200, help_text="Budget name")
    name_ar = models.CharField(max_length=200, help_text="Arabic budget name")
    department = models.ForeignKey(
        'core.Department',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='budgets'
    )
    project = models.ForeignKey(
        'projects.Project',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name='budgets'
    )
    fiscal_year = models.IntegerField(help_text="Fiscal year for this budget")
    total_amount = MoneyField(help_text="Total budget amount")
    allocated_amount = MoneyField(default=0, help_text="Amount allocated")
    spent_amount = MoneyField(default=0, help_text="Amount spent")
    remaining_amount = MoneyField(default=0, help_text="Remaining amount")
    start_date = models.DateField(help_text="Budget period start date")
    end_date = models.DateField(help_text="Budget period end date")
    is_active = models.BooleanField(default=True)
    created_by = models.ForeignKey(
        'core.Employee',
        on_delete=models.CASCADE,
        related_name='created_budgets'
    )

    def __str__(self):
        return f"{self.name} - {self.fiscal_year}"

    def calculate_remaining_amount(self):
        """Calculate and update remaining amount"""
        self.remaining_amount = self.total_amount - self.spent_amount
        return self.remaining_amount

    def get_utilization_percentage(self):
        """Get budget utilization percentage"""
        if self.total_amount > 0:
            return (self.spent_amount / self.total_amount) * 100
        return 0

    def is_over_budget(self):
        """Check if budget is exceeded"""
        return self.spent_amount > self.total_amount

    class Meta:
        ordering = ['-fiscal_year', 'name']
        indexes = [
            models.Index(fields=['department', 'fiscal_year']),
            models.Index(fields=['project', 'fiscal_year']),
            models.Index(fields=['fiscal_year', 'is_active']),
        ]