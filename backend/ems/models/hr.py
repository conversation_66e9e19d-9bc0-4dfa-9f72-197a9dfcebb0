"""
HR Domain Models for EMS
Contains human resources related models:
- EmployeeActivation: Employee account activation process
- LeaveType: Types of leave available
- LeaveRequest: Employee leave requests
- Attendance: Daily attendance tracking
- PerformanceReview: Employee performance reviews
- PayrollPeriod: Payroll processing periods
- PayrollEntry: Individual payroll records
- TrainingProgram: Training and development programs
"""

from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from django.core.validators import Min<PERSON><PERSON>ueValidator, MaxValueValidator
from datetime import timedelta
import uuid
from .base import BaseModel, NamedModel, AuditableModel, MoneyField, APPROVAL_STATUS_CHOICES


class EmployeeActivation(BaseModel):
    """
    Model to handle employee account activation process with admin approval
    """
    APPROVAL_STATUS_CHOICES = [
        ('PENDING', 'Pending Admin Approval'),
        ('APPROVED', 'Approved by Admin'),
        ('REJECTED', 'Rejected by Admin'),
    ]

    employee = models.OneToOneField(
        'core.Employee',
        on_delete=models.CASCADE,
        related_name='activation'
    )
    activation_token = models.UUIDField(default=uuid.uuid4, unique=True)
    expires_at = models.DateTimeField()
    is_activated = models.BooleanField(default=False)
    activated_at = models.DateTimeField(null=True, blank=True)
    email_sent = models.BooleanField(default=False)
    email_sent_at = models.DateTimeField(null=True, blank=True)

    # Approval fields
    approval_status = models.CharField(
        max_length=20,
        choices=APPROVAL_STATUS_CHOICES,
        default='PENDING'
    )
    approved_by = models.ForeignKey(
        'core.Employee',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_activations'
    )
    approved_at = models.DateTimeField(null=True, blank=True)
    rejection_reason = models.TextField(
        blank=True,
        help_text="Reason for rejection if applicable"
    )

    def save(self, *args, **kwargs):
        if not self.expires_at:
            # Token expires in 7 days
            self.expires_at = timezone.now() + timedelta(days=7)
        super().save(*args, **kwargs)

    def is_expired(self):
        return timezone.now() > self.expires_at

    def approve(self, approved_by_employee):
        """Approve the employee activation and send activation email"""
        self.approval_status = 'APPROVED'
        self.approved_by = approved_by_employee
        self.approved_at = timezone.now()
        self.save()

        # Send activation email after approval
        try:
            from ..email_service import EmailService
            email_sent = EmailService.send_employee_activation_email(
                self.employee,
                str(self.activation_token)
            )
            if email_sent:
                self.email_sent = True
                self.email_sent_at = timezone.now()
                self.save()
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(
                f"Failed to send activation email after approval "
                f"for employee {self.employee.employee_id}: {str(e)}"
            )

    def reject(self, approved_by_employee, reason=""):
        """Reject the employee activation"""
        self.approval_status = 'REJECTED'
        self.approved_by = approved_by_employee
        self.approved_at = timezone.now()
        self.rejection_reason = reason
        self.save()

        # Optionally send rejection email
        try:
            from ..email_service import EmailService
            EmailService.send_employee_rejection_email(self.employee, reason)
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(
                f"Failed to send rejection email "
                f"for employee {self.employee.employee_id}: {str(e)}"
            )

    def activate(self):
        """Mark the employee as activated (called when user completes activation)"""
        if self.approval_status != 'APPROVED':
            raise ValueError("Employee must be approved before activation")

        self.is_activated = True
        self.activated_at = timezone.now()
        # Set the user as active
        self.employee.user.is_active = True
        self.employee.user.save()
        self.save()

    def __str__(self):
        return f"Activation for {self.employee.user.get_full_name()}"

    class Meta:
        ordering = ['-created_at']


class LeaveType(NamedModel):
    """
    Types of leave available to employees
    """
    days_allowed = models.IntegerField(
        help_text="Number of days allowed per year",
        validators=[MinValueValidator(0)]
    )
    is_paid = models.BooleanField(default=True, help_text="Is this paid leave?")
    requires_approval = models.BooleanField(
        default=True,
        help_text="Does this leave type require approval?"
    )
    carry_forward = models.BooleanField(
        default=False,
        help_text="Can unused days be carried forward to next year?"
    )

    def __str__(self):
        return self.name

    class Meta:
        ordering = ['name']


class LeaveRequest(BaseModel):
    """
    Employee leave requests
    """
    STATUS_CHOICES = [
        ('PENDING', 'Pending'),
        ('APPROVED', 'Approved'),
        ('REJECTED', 'Rejected'),
        ('CANCELLED', 'Cancelled'),
    ]

    employee = models.ForeignKey(
        'core.Employee',
        on_delete=models.CASCADE,
        related_name='leave_requests'
    )
    leave_type = models.ForeignKey(
        LeaveType,
        on_delete=models.CASCADE,
        related_name='requests'
    )
    start_date = models.DateField(help_text="Leave start date")
    end_date = models.DateField(help_text="Leave end date")
    days_requested = models.IntegerField(
        help_text="Number of days requested",
        validators=[MinValueValidator(1)]
    )
    reason = models.TextField(help_text="Reason for leave")
    reason_ar = models.TextField(blank=True, help_text="Reason in Arabic")
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='PENDING'
    )
    approved_by = models.ForeignKey(
        'core.Employee',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='approved_leaves'
    )
    approval_date = models.DateTimeField(null=True, blank=True)
    rejection_reason = models.TextField(blank=True)
    rejection_reason_ar = models.TextField(blank=True, help_text="Rejection reason in Arabic")

    def __str__(self):
        return f"{self.employee} - {self.leave_type} ({self.start_date} to {self.end_date})"

    def approve(self, approved_by_employee):
        """Approve the leave request"""
        self.status = 'APPROVED'
        self.approved_by = approved_by_employee
        self.approval_date = timezone.now()
        self.save()

    def reject(self, approved_by_employee, reason="", reason_ar=""):
        """Reject the leave request"""
        self.status = 'REJECTED'
        self.approved_by = approved_by_employee
        self.approval_date = timezone.now()
        self.rejection_reason = reason
        self.rejection_reason_ar = reason_ar
        self.save()

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['employee', 'status']),
            models.Index(fields=['start_date', 'end_date']),
            models.Index(fields=['status', 'created_at']),
        ]


class Attendance(BaseModel):
    """
    Daily attendance tracking for employees
    """
    employee = models.ForeignKey(
        'core.Employee',
        on_delete=models.CASCADE,
        related_name='attendance_records'
    )
    date = models.DateField(help_text="Attendance date")
    check_in = models.TimeField(null=True, blank=True, help_text="Check-in time")
    check_out = models.TimeField(null=True, blank=True, help_text="Check-out time")
    break_start = models.TimeField(null=True, blank=True, help_text="Break start time")
    break_end = models.TimeField(null=True, blank=True, help_text="Break end time")
    total_hours = models.DecimalField(
        max_digits=4,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Total hours worked"
    )
    overtime_hours = models.DecimalField(
        max_digits=4,
        decimal_places=2,
        default=0,
        help_text="Overtime hours"
    )
    is_present = models.BooleanField(default=True, help_text="Was employee present?")
    is_late = models.BooleanField(default=False, help_text="Was employee late?")
    notes = models.TextField(blank=True, help_text="Additional notes")

    def __str__(self):
        return f"{self.employee} - {self.date}"

    def calculate_total_hours(self):
        """Calculate total hours worked"""
        if self.check_in and self.check_out:
            from datetime import datetime, timedelta

            # Convert times to datetime for calculation
            check_in_dt = datetime.combine(self.date, self.check_in)
            check_out_dt = datetime.combine(self.date, self.check_out)

            # Handle overnight shifts
            if check_out_dt < check_in_dt:
                check_out_dt += timedelta(days=1)

            # Calculate break time
            break_time = timedelta()
            if self.break_start and self.break_end:
                break_start_dt = datetime.combine(self.date, self.break_start)
                break_end_dt = datetime.combine(self.date, self.break_end)
                if break_end_dt > break_start_dt:
                    break_time = break_end_dt - break_start_dt

            # Calculate total hours
            total_time = check_out_dt - check_in_dt - break_time
            self.total_hours = round(total_time.total_seconds() / 3600, 2)

        return self.total_hours

    class Meta:
        unique_together = ['employee', 'date']
        ordering = ['-date']
        indexes = [
            models.Index(fields=['employee', 'date']),
            models.Index(fields=['date', 'is_present']),
            models.Index(fields=['employee', '-date']),
        ]


class PerformanceReview(BaseModel):
    """
    Employee performance reviews
    """
    REVIEW_TYPES = [
        ('annual', 'Annual Review'),
        ('quarterly', 'Quarterly Review'),
        ('probation', 'Probation Review'),
        ('project', 'Project Review'),
    ]

    RATING_CHOICES = [
        (1, 'Poor'),
        (2, 'Below Average'),
        (3, 'Average'),
        (4, 'Good'),
        (5, 'Excellent'),
    ]

    employee = models.ForeignKey(
        'core.Employee',
        on_delete=models.CASCADE,
        related_name='performance_reviews'
    )
    reviewer = models.ForeignKey(
        'core.Employee',
        on_delete=models.CASCADE,
        related_name='conducted_reviews'
    )
    review_period_start = models.DateField(help_text="Review period start date")
    review_period_end = models.DateField(help_text="Review period end date")
    review_type = models.CharField(
        max_length=20,
        choices=REVIEW_TYPES,
        default='annual'
    )

    # Rating fields
    overall_rating = models.IntegerField(choices=RATING_CHOICES)
    goals_achievement = models.IntegerField(choices=RATING_CHOICES)
    communication_skills = models.IntegerField(choices=RATING_CHOICES)
    teamwork = models.IntegerField(choices=RATING_CHOICES)
    leadership = models.IntegerField(choices=RATING_CHOICES, null=True, blank=True)
    technical_skills = models.IntegerField(choices=RATING_CHOICES)

    # Text feedback
    strengths = models.TextField(help_text="Employee strengths")
    areas_for_improvement = models.TextField(help_text="Areas for improvement")
    goals_for_next_period = models.TextField(help_text="Goals for next period")
    reviewer_comments = models.TextField(help_text="Reviewer comments")
    employee_comments = models.TextField(blank=True, help_text="Employee comments")
    hr_comments = models.TextField(blank=True, help_text="HR comments")

    is_final = models.BooleanField(default=False, help_text="Is this review finalized?")

    def __str__(self):
        return f"{self.employee.user.first_name} {self.employee.user.last_name} - {self.review_type} ({self.review_period_start})"

    def calculate_average_rating(self):
        """Calculate average rating across all categories"""
        ratings = [
            self.overall_rating,
            self.goals_achievement,
            self.communication_skills,
            self.teamwork,
            self.technical_skills,
        ]
        if self.leadership:
            ratings.append(self.leadership)

        return sum(ratings) / len(ratings)

    class Meta:
        ordering = ['-review_period_end']
        indexes = [
            models.Index(fields=['employee', '-review_period_end']),
            models.Index(fields=['reviewer', '-review_period_end']),
            models.Index(fields=['review_type', '-review_period_end']),
        ]


class PayrollPeriod(BaseModel):
    """
    Payroll processing periods
    """
    name = models.CharField(max_length=100, help_text="Payroll period name")
    start_date = models.DateField(help_text="Period start date")
    end_date = models.DateField(help_text="Period end date")
    is_processed = models.BooleanField(default=False, help_text="Has payroll been processed?")

    def __str__(self):
        return f"{self.name} ({self.start_date} - {self.end_date})"

    class Meta:
        ordering = ['-start_date']
        indexes = [
            models.Index(fields=['-start_date']),
            models.Index(fields=['is_processed', '-start_date']),
        ]


class PayrollEntry(BaseModel):
    """
    Individual payroll records for employees
    """
    employee = models.ForeignKey(
        'core.Employee',
        on_delete=models.CASCADE,
        related_name='payroll_entries'
    )
    payroll_period = models.ForeignKey(
        PayrollPeriod,
        on_delete=models.CASCADE,
        related_name='entries'
    )

    # Salary components
    basic_salary = MoneyField(help_text="Basic salary amount")
    allowances = MoneyField(default=0, help_text="Additional allowances")
    overtime_hours = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=0,
        help_text="Overtime hours worked"
    )
    overtime_rate = MoneyField(default=0, help_text="Overtime hourly rate")
    bonuses = MoneyField(default=0, help_text="Performance bonuses")

    # Deductions
    deductions = MoneyField(default=0, help_text="Other deductions")
    tax_deduction = MoneyField(default=0, help_text="Tax deductions")
    insurance_deduction = MoneyField(default=0, help_text="Insurance deductions")

    # Final amounts
    net_salary = MoneyField(help_text="Net salary after deductions")
    is_paid = models.BooleanField(default=False, help_text="Has salary been paid?")
    payment_date = models.DateField(null=True, blank=True, help_text="Date of payment")

    def save(self, *args, **kwargs):
        # Calculate net salary
        gross_salary = (
            self.basic_salary +
            self.allowances +
            (self.overtime_hours * self.overtime_rate) +
            self.bonuses
        )
        total_deductions = (
            self.deductions +
            self.tax_deduction +
            self.insurance_deduction
        )
        self.net_salary = gross_salary - total_deductions
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.employee.user.first_name} {self.employee.user.last_name} - {self.payroll_period.name}"

    class Meta:
        unique_together = ['employee', 'payroll_period']
        ordering = ['-payroll_period__start_date']
        indexes = [
            models.Index(fields=['employee', '-payroll_period__start_date']),
            models.Index(fields=['payroll_period', 'is_paid']),
        ]


class TrainingProgram(BaseModel):
    """
    Training and development programs
    """
    STATUS_CHOICES = [
        ('planned', 'Planned'),
        ('active', 'Active'),
        ('completed', 'Completed'),
        ('cancelled', 'Cancelled'),
    ]

    title = models.CharField(max_length=200, help_text="Training program title")
    title_ar = models.CharField(max_length=200, blank=True, help_text="Arabic title")
    description = models.TextField(help_text="Program description")
    description_ar = models.TextField(blank=True, help_text="Arabic description")
    instructor = models.CharField(max_length=200, help_text="Instructor name")
    department = models.ForeignKey(
        'core.Department',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='training_programs'
    )
    start_date = models.DateField(help_text="Training start date")
    end_date = models.DateField(help_text="Training end date")
    duration_hours = models.IntegerField(
        help_text="Duration in hours",
        validators=[MinValueValidator(1)]
    )
    max_participants = models.IntegerField(
        help_text="Maximum number of participants",
        validators=[MinValueValidator(1)]
    )
    cost_per_participant = MoneyField(
        default=0,
        help_text="Cost per participant"
    )
    location = models.CharField(max_length=200, blank=True, help_text="Training location")
    is_mandatory = models.BooleanField(
        default=False,
        help_text="Is this training mandatory?"
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='planned'
    )
    created_by = models.ForeignKey(
        'core.Employee',
        on_delete=models.CASCADE,
        related_name='created_training_programs'
    )

    def __str__(self):
        return self.title

    def get_participant_count(self):
        """Get current number of participants"""
        return self.participants.count()

    def is_full(self):
        """Check if training program is at capacity"""
        return self.get_participant_count() >= self.max_participants

    class Meta:
        ordering = ['-start_date']
        indexes = [
            models.Index(fields=['-start_date']),
            models.Index(fields=['status', '-start_date']),
            models.Index(fields=['department', '-start_date']),
        ]