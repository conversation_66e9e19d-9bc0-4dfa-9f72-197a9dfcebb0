import React, { useState, useEffect } from 'react';
import LiveChatWidget from './LiveChatWidget';

interface LiveChatIntegrationProps {
  language?: 'ar' | 'en';
  autoStart?: boolean;
  customerInfo?: {
    name?: string;
    email?: string;
    phone?: string;
  };
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  theme?: 'default' | 'minimal' | 'enterprise';
  showOnPages?: string[]; // Array of page paths where chat should be shown
  hideOnPages?: string[]; // Array of page paths where chat should be hidden
}

const LiveChatIntegration: React.FC<LiveChatIntegrationProps> = ({
  language = 'en',
  autoStart = false,
  customerInfo,
  position = 'bottom-right',
  theme = 'default',
  showOnPages,
  hideOnPages
}) => {
  const [shouldShow, setShouldShow] = useState(true);
  const [currentPath, setCurrentPath] = useState('');

  useEffect(() => {
    // Get current path
    setCurrentPath(window.location.pathname);

    // Listen for route changes
    const handleRouteChange = (): void => {
      setCurrentPath(window.location.pathname);
    };

    window.addEventListener('popstate', handleRouteChange);
    
    // FIXED: Use React Router's useLocation instead of MutationObserver
    // MutationObserver on document.body with subtree: true is extremely expensive
    // This is a temporary fix - should use React Router's useLocation hook

    // REMOVED: Expensive MutationObserver that monitors entire DOM
    // const observer = new MutationObserver(() => {
    //   if (window.location.pathname !== currentPath) {
    //     setCurrentPath(window.location.pathname);
    //   }
    // });
    // observer.observe(document.body, { childList: true, subtree: true });

    return () => {
      window.removeEventListener('popstate', handleRouteChange);
      // FIXED: Removed observer.disconnect() since we removed the observer
    };
  }, [currentPath]);

  useEffect(() => {
    // Determine if chat should be shown based on current path
    let show = true;

    if (showOnPages && showOnPages.length > 0) {
      show = showOnPages.some(page => currentPath.startsWith(page));
    }

    if (hideOnPages && hideOnPages.length > 0) {
      show = show && !hideOnPages.some(page => currentPath.startsWith(page));
    }

    setShouldShow(show);
  }, [currentPath, showOnPages, hideOnPages]);

  // Don't render if chat should be hidden on this page
  if (!shouldShow) {
    return null;
  }

  return (
    <div className={`live-chat-integration ${position} ${theme}`}>
      <LiveChatWidget
        language={language}
        customerInfo={customerInfo}
      />
    </div>
  );
};

// Higher-order component for easy integration
export const withLiveChat = <P extends object>(
  Component: React.ComponentType<P>,
  chatConfig?: Omit<LiveChatIntegrationProps, 'children'>
) => {
  return (props: P) => (
    <>
      <Component {...props} />
      <LiveChatIntegration {...chatConfig} />
    </>
  );
};

// Hook for programmatic chat control
export const useLiveChat = (): void => {
  const [isOpen, setIsOpen] = useState(false);
  const [hasUnreadMessages, setHasUnreadMessages] = useState(false);

  const openChat = (): void => {
    setIsOpen(true);
    setHasUnreadMessages(false);
  };

  const closeChat = (): void => {
    setIsOpen(false);
  };

  const toggleChat = (): void => {
    setIsOpen(!isOpen);
    if (!isOpen) {
      setHasUnreadMessages(false);
    }
  };

  const markAsRead = (): void => {
    setHasUnreadMessages(false);
  };

  const markAsUnread = (): void => {
    setHasUnreadMessages(true);
  };

  return {
    isOpen,
    hasUnreadMessages,
    openChat,
    closeChat,
    toggleChat,
    markAsRead,
    markAsUnread
  };
};

// Context for global chat state management
export const LiveChatContext = React.createContext<{
  isEnabled: boolean;
  language: 'ar' | 'en';
  customerInfo?: {
    name?: string;
    email?: string;
    phone?: string;
  };
  setCustomerInfo: (info: any) => void;
  setLanguage: (lang: 'ar' | 'en') => void;
  setEnabled: (enabled: boolean) => void;
}>({
  isEnabled: true,
  language: 'en',
  setCustomerInfo: () => {},
  setLanguage: () => {},
  setEnabled: () => {}
});

export const LiveChatProvider: React.FC<{
  children: React.ReactNode;
  defaultLanguage?: 'ar' | 'en';
  defaultCustomerInfo?: any;
}> = ({ children, defaultLanguage = 'en', defaultCustomerInfo }) => {
  const [isEnabled, setEnabled] = useState(true);
  const [language, setLanguage] = useState<'ar' | 'en'>(defaultLanguage);
  const [customerInfo, setCustomerInfo] = useState(defaultCustomerInfo);

  return (
    <LiveChatContext.Provider
      value={{
        isEnabled,
        language,
        customerInfo,
        setCustomerInfo,
        setLanguage,
        setEnabled
      }}
    >
      {children}
    </LiveChatContext.Provider>
  );
};

// Utility component for triggering chat from anywhere in the app
export const LiveChatTrigger: React.FC<{
  children: React.ReactNode;
  message?: string;
  className?: string;
}> = ({ children, message, className }) => {
  const handleClick = (): void => {
    // Dispatch custom event to open chat
    window.dispatchEvent(new CustomEvent('openLiveChat', {
      detail: { message }
    }));
  };

  return (
    <div onClick={handleClick} className={className} style={{ cursor: 'pointer' }}>
      {children}
    </div>
  );
};

// Component for displaying chat status
export const LiveChatStatus: React.FC<{
  language?: 'ar' | 'en';
  showAgentCount?: boolean;
  showWaitTime?: boolean;
}> = ({ language = 'en', showAgentCount = true, showWaitTime = true }) => {
  const [status, setStatus] = useState<{
    agentsOnline: number;
    avgWaitTime: string;
    isOnline: boolean;
  }>({
    agentsOnline: 0,
    avgWaitTime: '0m',
    isOnline: false
  });

  useEffect(() => {
    // Fetch chat status
    const fetchStatus = async () => {
      try {
        const response = await fetch('/api/customer-service/live-chat/status/');
        if (response.ok) {
          const data = await response.json();
          setStatus(data);
        }
      } catch (error) {
        console.error('Error fetching chat status:', error);
      }
    };

    fetchStatus();
    const interval = setInterval(fetchStatus, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="live-chat-status flex items-center gap-2 text-sm">
      <div className={`w-2 h-2 rounded-full ${status.isOnline ? 'bg-green-500' : 'bg-red-500'}`}></div>
      <span className="text-white/80">
        {status.isOnline 
          ? (language === 'ar' ? 'الدعم متاح' : 'Support Available')
          : (language === 'ar' ? 'الدعم غير متاح' : 'Support Offline')
        }
      </span>
      
      {status.isOnline && showAgentCount && (
        <span className="text-white/60">
          • {status.agentsOnline} {language === 'ar' ? 'وكيل متاح' : 'agents online'}
        </span>
      )}
      
      {status.isOnline && showWaitTime && (
        <span className="text-white/60">
          • {language === 'ar' ? 'وقت الانتظار' : 'wait time'} ~{status.avgWaitTime}
        </span>
      )}
    </div>
  );
};

// Pre-configured chat for different use cases
export const SupportChat: React.FC<{ language?: 'ar' | 'en' }> = ({ language }) => (
  <LiveChatIntegration
    language={language}
    theme="default"
    hideOnPages={['/admin', '/dashboard']}
  />
);

export const SalesChat: React.FC<{ language?: 'ar' | 'en' }> = ({ language }) => (
  <LiveChatIntegration
    language={language}
    theme="enterprise"
    showOnPages={['/products', '/pricing', '/contact']}
  />
);

export const AdminChat: React.FC<{ language?: 'ar' | 'en' }> = ({ language }) => (
  <LiveChatIntegration
    language={language}
    theme="minimal"
    showOnPages={['/admin', '/dashboard']}
  />
);

export default LiveChatIntegration;
