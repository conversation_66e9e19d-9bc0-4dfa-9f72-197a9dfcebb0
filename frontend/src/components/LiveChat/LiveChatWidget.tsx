import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
// import { Textarea } from '@/components/ui/textarea'; // TODO: Add message composition area
import {
  MessageCircle,
  Send,
  X,
  Minimize2,
  Maximize2,
  User,
  // Bot, // TODO: Add bot message indicators
  Phone,
  // Mail, // TODO: Add email integration
  Clock,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react';

interface ChatMessage {
  id: string;
  content: string;
  sender: string;
  sender_name: string;
  message_type: 'text' | 'system' | 'file';
  created_at: string;
  is_read: boolean;
}

interface LiveChatSession {
  session_id: string;
  status: 'waiting' | 'active' | 'ended';
  agent_assigned: boolean;
  customer_created: boolean;
  started_at: string;
}

interface LiveChatWidgetProps {
  language?: 'ar' | 'en';
  customerInfo?: {
    name?: string;
    email?: string;
    phone?: string;
  };
}

const LiveChatWidget: React.FC<LiveChatWidgetProps> = ({ 
  language = 'en',
  customerInfo 
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [session, setSession] = useState<LiveChatSession | null>(null);
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected'>('disconnected');
  const [unreadCount, setUnreadCount] = useState(0);
  const [isTyping, setIsTyping] = useState(false);
  const [customerForm, setCustomerForm] = useState({
    name: customerInfo?.name || '',
    email: customerInfo?.email || '',
    phone: customerInfo?.phone || '',
    subject: ''
  });
  const [showCustomerForm, setShowCustomerForm] = useState(!customerInfo?.email);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const pollIntervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (isOpen && !isMinimized) {
      scrollToBottom();
      setUnreadCount(0);
    }
  }, [messages, isOpen, isMinimized]);

  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen, isMinimized]);

  useEffect(() => {
    // Start polling for new messages when session is active
    if (session && session.status === 'active') {
      startMessagePolling();
    } else {
      stopMessagePolling();
    }

    return () => stopMessagePolling();
  }, [session]);

  const scrollToBottom = (): void => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const startMessagePolling = (): void => {
    if (pollIntervalRef.current) return;
    
    pollIntervalRef.current = setInterval(() => {
      if (session) {
        fetchMessages();
      }
    }, 3000); // Poll every 3 seconds
  };

  const stopMessagePolling = (): void => {
    if (pollIntervalRef.current) {
      clearInterval(pollIntervalRef.current);
      pollIntervalRef.current = null;
    }
  };

  const startChatSession = async () => {
    if (showCustomerForm && (!customerForm.name || !customerForm.email)) {
      return;
    }

    setIsLoading(true);
    setConnectionStatus('connecting');

    try {
      const response = await fetch('/api/live-chat/start/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          customer_name: customerForm.name,
          customer_email: customerForm.email,
          customer_phone: customerForm.phone,
          subject: customerForm.subject || 'Live Chat Support',
          language: language
        })
      });

      if (response.ok) {
        const sessionData = await response.json();
        setSession(sessionData);
        setConnectionStatus('connected');
        setShowCustomerForm(false);
        
        // Fetch initial messages
        await fetchMessages(sessionData.session_id);
        
        // Add welcome message
        const welcomeMessage: ChatMessage = {
          id: 'welcome',
          content: language === 'ar' 
            ? `مرحباً ${customerForm.name}! تم بدء جلسة الدردشة. ${sessionData.agent_assigned ? 'تم تعيين وكيل لمساعدتك.' : 'يرجى الانتظار حتى يتم تعيين وكيل متاح.'}`
            : `Hello ${customerForm.name}! Chat session started. ${sessionData.agent_assigned ? 'An agent has been assigned to help you.' : 'Please wait while we assign an available agent.'}`,
          sender: 'System',
          sender_name: 'System',
          message_type: 'system',
          created_at: new Date().toISOString(),
          is_read: true
        };
        
        setMessages(prev => [welcomeMessage, ...prev]);
      } else {
        throw new Error('Failed to start chat session');
      }
    } catch (error) {
      console.error('Error starting chat:', error);
      setConnectionStatus('disconnected');
      
      const errorMessage: ChatMessage = {
        id: 'error',
        content: language === 'ar' 
          ? 'عذراً، لا يمكن بدء جلسة الدردشة. يرجى المحاولة مرة أخرى أو الاتصال بالدعم مباشرة.'
          : 'Sorry, unable to start chat session. Please try again or contact support directly.',
        sender: 'System',
        sender_name: 'System',
        message_type: 'system',
        created_at: new Date().toISOString(),
        is_read: true
      };
      
      setMessages([errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchMessages = async (sessionId?: string) => {
    if (!session && !sessionId) return;

    try {
      const id = sessionId || session!.session_id;
      const response = await fetch(`/api/live-chat/messages/?session_id=${id}`);
      
      if (response.ok) {
        const data = await response.json();
        const newMessages = data.messages || [];
        
        setMessages(prev => {
          const existingIds = new Set(prev.map(m => m.id));
          const uniqueNewMessages = newMessages.filter((m: ChatMessage) => !existingIds.has(m.id));
          
          if (uniqueNewMessages.length > 0 && !isOpen) {
            setUnreadCount(prev => prev + uniqueNewMessages.length);
          }
          
          return [...prev, ...uniqueNewMessages].sort((a, b) => 
            new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
          );
        });
      }
    } catch (error) {
      console.error('Error fetching messages:', error);
    }
  };

  const sendMessage = async () => {
    if (!inputMessage.trim() || !session || isLoading) return;

    const messageContent = inputMessage.trim();
    setInputMessage('');
    setIsLoading(true);

    // Add user message immediately
    const userMessage: ChatMessage = {
      id: `temp-${Date.now()}`,
      content: messageContent,
      sender: customerForm.name || 'You',
      sender_name: customerForm.name || 'You',
      message_type: 'text',
      created_at: new Date().toISOString(),
      is_read: true
    };

    setMessages(prev => [...prev, userMessage]);

    try {
      const response = await fetch('/api/live-chat/send/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          session_id: session.session_id,
          content: messageContent
        })
      });

      if (response.ok) {
        // Remove temp message and fetch updated messages
        setMessages(prev => prev.filter(m => m.id !== userMessage.id));
        await fetchMessages();
      } else {
        throw new Error('Failed to send message');
      }
    } catch (error) {
      console.error('Error sending message:', error);
      
      // Update temp message to show error
      setMessages(prev => prev.map(m => 
        m.id === userMessage.id 
          ? { ...m, content: `${m.content} ❌ Failed to send`, message_type: 'system' as const }
          : m
      ));
    } finally {
      setIsLoading(false);
    }
  };

  const endChatSession = async () => {
    if (!session) return;

    try {
      // For now, just close the widget
      // In a full implementation, you'd call the end session API
      setSession(null);
      setMessages([]);
      setIsOpen(false);
      setConnectionStatus('disconnected');
      stopMessagePolling();
    } catch (error) {
      console.error('Error ending chat:', error);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent): void => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const formatTime = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleTimeString(language === 'ar' ? 'ar-SA' : 'en-US', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const getStatusColor = (): void => {
    switch (connectionStatus) {
      case 'connected': return 'bg-green-500';
      case 'connecting': return 'bg-yellow-500';
      default: return 'bg-red-500';
    }
  };

  const getStatusText = (): void => {
    switch (connectionStatus) {
      case 'connected': return language === 'ar' ? 'متصل' : 'Connected';
      case 'connecting': return language === 'ar' ? 'جاري الاتصال...' : 'Connecting...';
      default: return language === 'ar' ? 'غير متصل' : 'Disconnected';
    }
  };

  // Chat Widget Button (when closed)
  if (!isOpen) {
    return (
      <div className="fixed bottom-6 right-6 z-50">
        <Button
          onClick={() => setIsOpen(true)}
          className="w-14 h-14 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 shadow-lg hover:shadow-xl transition-all duration-300 relative"
        >
          <MessageCircle className="h-6 w-6 text-white" />
          {unreadCount > 0 && (
            <div className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center text-white text-xs font-bold animate-pulse">
              {unreadCount > 9 ? '9+' : unreadCount}
            </div>
          )}
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-6 right-6 z-50">
      <Card className={`glass-card border-white/20 transition-all duration-300 ${
        isMinimized ? 'w-80 h-16' : isExpanded ? 'w-[500px] h-[700px]' : 'w-96 h-[600px]'
      } flex flex-col`}>
        {/* Header */}
        <CardHeader className="border-b border-white/10 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                <MessageCircle className="h-4 w-4 text-white" />
              </div>
              <div>
                <CardTitle className="text-white text-sm">
                  {language === 'ar' ? 'الدردشة المباشرة' : 'Live Chat'}
                </CardTitle>
                <div className="flex items-center gap-1">
                  <div className={`w-2 h-2 rounded-full ${getStatusColor()}`}></div>
                  <span className="text-xs text-white/80">{getStatusText()}</span>
                </div>
              </div>
            </div>
            <div className="flex gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsExpanded(!isExpanded)}
                className="text-white/60 hover:text-white h-8 w-8 p-0"
              >
                {isExpanded ? <Minimize2 className="h-4 w-4" /> : <Maximize2 className="h-4 w-4" />}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsMinimized(!isMinimized)}
                className="text-white/60 hover:text-white h-8 w-8 p-0"
              >
                <Minimize2 className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsOpen(false)}
                className="text-white/60 hover:text-white h-8 w-8 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardHeader>

        {/* Content (hidden when minimized) */}
        {!isMinimized && (
          <>
            <CardContent className="flex-1 overflow-hidden p-0">
              {/* Customer Form */}
              {showCustomerForm && (
                <div className="p-4 border-b border-white/10">
                  <h3 className="text-white text-sm font-medium mb-3">
                    {language === 'ar' ? 'معلومات الاتصال' : 'Contact Information'}
                  </h3>
                  <div className="space-y-2">
                    <Input
                      placeholder={language === 'ar' ? 'الاسم' : 'Name'}
                      value={customerForm.name}
                      onChange={(e) => setCustomerForm(prev => ({ ...prev, name: e.target.value }))}
                      className="bg-white/5 border-white/20 text-white placeholder:text-white/40 text-sm h-8"
                    />
                    <Input
                      type="email"
                      placeholder={language === 'ar' ? 'البريد الإلكتروني' : 'Email'}
                      value={customerForm.email}
                      onChange={(e) => setCustomerForm(prev => ({ ...prev, email: e.target.value }))}
                      className="bg-white/5 border-white/20 text-white placeholder:text-white/40 text-sm h-8"
                    />
                    <Input
                      placeholder={language === 'ar' ? 'الموضوع (اختياري)' : 'Subject (optional)'}
                      value={customerForm.subject}
                      onChange={(e) => setCustomerForm(prev => ({ ...prev, subject: e.target.value }))}
                      className="bg-white/5 border-white/20 text-white placeholder:text-white/40 text-sm h-8"
                    />
                    <Button
                      onClick={startChatSession}
                      disabled={!customerForm.name || !customerForm.email || isLoading}
                      className="w-full bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 h-8 text-sm"
                    >
                      {isLoading ? (
                        <Loader2 className="h-3 w-3 animate-spin mr-1" />
                      ) : null}
                      {language === 'ar' ? 'بدء الدردشة' : 'Start Chat'}
                    </Button>
                  </div>
                </div>
              )}

              {/* Messages */}
              <div className="flex-1 overflow-y-auto p-4 space-y-3 max-h-96">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex gap-2 ${
                      message.sender === customerForm.name || message.sender === 'You' 
                        ? 'justify-end' 
                        : 'justify-start'
                    }`}
                  >
                    {message.sender !== customerForm.name && message.sender !== 'You' && (
                      <div className={`w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 ${
                        message.message_type === 'system' ? 'bg-orange-500' : 'bg-blue-500'
                      }`}>
                        {message.message_type === 'system' ? (
                          <AlertCircle className="h-3 w-3 text-white" />
                        ) : (
                          <User className="h-3 w-3 text-white" />
                        )}
                      </div>
                    )}

                    <div className={`max-w-[80%] ${
                      message.sender === customerForm.name || message.sender === 'You' ? 'order-first' : ''
                    }`}>
                      <div
                        className={`p-2 rounded-lg text-sm ${
                          message.sender === customerForm.name || message.sender === 'You'
                            ? 'bg-blue-500 text-white ml-auto'
                            : message.message_type === 'system'
                            ? 'bg-orange-500/20 text-orange-300 border border-orange-500/30'
                            : 'bg-white/10 text-white'
                        }`}
                      >
                        <p>{message.content}</p>
                      </div>

                      <div className={`text-xs text-white/60 mt-1 ${
                        message.sender === customerForm.name || message.sender === 'You' ? 'text-right' : ''
                      }`}>
                        {formatTime(message.created_at)}
                      </div>
                    </div>

                    {(message.sender === customerForm.name || message.sender === 'You') && (
                      <div className="w-6 h-6 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center flex-shrink-0">
                        <User className="h-3 w-3 text-white" />
                      </div>
                    )}
                  </div>
                ))}

                {/* Typing indicator */}
                {isTyping && (
                  <div className="flex gap-2 justify-start">
                    <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                      <User className="h-3 w-3 text-white" />
                    </div>
                    <div className="bg-white/10 text-white p-2 rounded-lg">
                      <div className="flex items-center gap-1">
                        <div className="flex space-x-1">
                          <div className="w-1 h-1 bg-white rounded-full animate-bounce"></div>
                          <div className="w-1 h-1 bg-white rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                          <div className="w-1 h-1 bg-white rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                        </div>
                        <span className="text-xs ml-2">
                          {language === 'ar' ? 'يكتب...' : 'typing...'}
                        </span>
                      </div>
                    </div>
                  </div>
                )}

                <div ref={messagesEndRef} />
              </div>
            </CardContent>

            {/* Input (only show if session is active) */}
            {session && !showCustomerForm && (
              <div className="border-t border-white/10 p-3">
                <div className="flex gap-2 mb-2">
                  <Input
                    ref={inputRef}
                    value={inputMessage}
                    onChange={(e) => setInputMessage(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder={language === 'ar' ? 'اكتب رسالتك...' : 'Type your message...'}
                    className="flex-1 bg-white/5 border-white/20 text-white placeholder:text-white/40 text-sm h-8"
                    disabled={isLoading || connectionStatus !== 'connected'}
                  />
                  <Button
                    onClick={sendMessage}
                    disabled={!inputMessage.trim() || isLoading || connectionStatus !== 'connected'}
                    className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 h-8 w-8 p-0"
                  >
                    {isLoading ? (
                      <Loader2 className="h-3 w-3 animate-spin" />
                    ) : (
                      <Send className="h-3 w-3" />
                    )}
                  </Button>
                </div>
                
                {/* Session info and actions */}
                <div className="flex items-center justify-between text-xs text-white/60">
                  <div className="flex items-center gap-2">
                    {session.agent_assigned ? (
                      <Badge className="bg-green-500/20 text-green-300 border-green-500/30">
                        {language === 'ar' ? 'وكيل متصل' : 'Agent Connected'}
                      </Badge>
                    ) : (
                      <Badge className="bg-yellow-500/20 text-yellow-300 border-yellow-500/30">
                        {language === 'ar' ? 'في الانتظار' : 'Waiting for Agent'}
                      </Badge>
                    )}
                  </div>
                  <Button
                    onClick={endChatSession}
                    variant="ghost"
                    className="text-white/60 hover:text-red-400 h-6 px-2 text-xs"
                  >
                    {language === 'ar' ? 'إنهاء الدردشة' : 'End Chat'}
                  </Button>
                </div>
              </div>
            )}
          </>
        )}
      </Card>
    </div>
  );
};

export default LiveChatWidget;
