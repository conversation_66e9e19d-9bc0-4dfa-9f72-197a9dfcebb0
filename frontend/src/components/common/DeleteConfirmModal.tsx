/**
 * Delete Confirmation Modal
 * Reusable modal for confirming delete operations
 */

import React, { useState } from 'react'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { AlertTriangle, Trash2 } from 'lucide-react'

interface DeleteConfirmModalProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: () => Promise<void>
  title: string
  message: string
  itemName?: string
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    confirmDelete: 'تأكيد الحذف',
    deleteWarning: 'تحذير: هذا الإجراء لا يمكن التراجع عنه',
    cancel: 'إلغاء',
    delete: 'حذف',
    deleting: 'جاري الحذف...',
    areYouSure: 'هل أنت متأكد من أنك تريد حذف',
    thisAction: 'هذا الإجراء لا يمكن التراجع عنه'
  },
  en: {
    confirmDelete: 'Confirm Delete',
    deleteWarning: 'Warning: This action cannot be undone',
    cancel: 'Cancel',
    delete: 'Delete',
    deleting: 'Deleting...',
    areYouSure: 'Are you sure you want to delete',
    thisAction: 'This action cannot be undone'
  }
}

export default function DeleteConfirmModal({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  itemName,
  language
}: DeleteConfirmModalProps): void {
  const [isDeleting, setIsDeleting] = useState(false)
  const t = translations[language]
  const isRTL = language === 'ar'

  const handleConfirm = async () => {
    try {
      setIsDeleting(true)
      await onConfirm()
      onClose()
    } catch (error) {
      console.error('Delete error:', error)
    } finally {
      setIsDeleting(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className={`glass-card border-white/20 max-w-md ${isRTL ? 'rtl' : 'ltr'}`}>
        <DialogHeader>
          <div className="flex items-center gap-3">
            <div className="flex-shrink-0 w-10 h-10 bg-red-500/20 rounded-full flex items-center justify-center">
              <AlertTriangle className="h-5 w-5 text-red-400" />
            </div>
            <div>
              <DialogTitle className="text-white text-lg">
                {title || t.confirmDelete}
              </DialogTitle>
              <DialogDescription className="text-red-300 text-sm mt-1">
                {t.deleteWarning}
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className="py-4">
          <p className="text-white/80 text-sm leading-relaxed">
            {message || (
              <>
                {t.areYouSure} {itemName && <span className="font-semibold text-white">"{itemName}"</span>}?
              </>
            )}
          </p>
          <p className="text-white/60 text-xs mt-2">
            {t.thisAction}
          </p>
        </div>

        <DialogFooter className="flex gap-2">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            className="glass-button"
            disabled={isDeleting}
          >
            {t.cancel}
          </Button>
          <Button
            type="button"
            onClick={handleConfirm}
            className="bg-red-600 hover:bg-red-700 text-white"
            disabled={isDeleting}
          >
            {isDeleting ? (
              <>
                <Trash2 className="h-4 w-4 mr-2 animate-spin" />
                {t.deleting}
              </>
            ) : (
              <>
                <Trash2 className="h-4 w-4 mr-2" />
                {t.delete}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
