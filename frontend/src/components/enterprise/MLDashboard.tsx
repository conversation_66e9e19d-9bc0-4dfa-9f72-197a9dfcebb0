import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  Brain,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Users,
  Target,
  Zap,
  BarChart3,
  RefreshCw,
  Settings,
  Download
} from 'lucide-react'
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts'
import mlService, { MLModel, EmployeePerformancePrediction, TurnoverRiskPrediction } from '@/services/mlService'

interface MLDashboardProps {
  language: 'ar' | 'en'
  userRole: string
}

const translations = {
  ar: {
    mlDashboard: 'لوحة الذكاء الاصطناعي',
    modelPerformance: 'أداء النماذج',
    predictions: 'التوقعات',
    insights: 'الرؤى الذكية',
    recommendations: 'التوصيات',
    employeePerformance: 'أداء الموظفين',
    turnoverRisk: 'مخاطر دوران الموظفين',
    projectSuccess: 'نجاح المشاريع',
    budgetForecasting: 'توقعات الميزانية',
    modelAccuracy: 'دقة النموذج',
    lastTrained: 'آخر تدريب',
    retrain: 'إعادة تدريب',
    highRisk: 'مخاطرة عالية',
    mediumRisk: 'مخاطرة متوسطة',
    lowRisk: 'مخاطرة منخفضة',
    confidence: 'مستوى الثقة',
    trend: 'الاتجاه',
    improving: 'تحسن',
    declining: 'تراجع',
    stable: 'مستقر',
    exportReport: 'تصدير التقرير',
    configure: 'تكوين',
    refresh: 'تحديث'
  },
  en: {
    mlDashboard: 'AI & Machine Learning Dashboard',
    modelPerformance: 'Model Performance',
    predictions: 'Predictions',
    insights: 'Smart Insights',
    recommendations: 'Recommendations',
    employeePerformance: 'Employee Performance',
    turnoverRisk: 'Turnover Risk',
    projectSuccess: 'Project Success',
    budgetForecasting: 'Budget Forecasting',
    modelAccuracy: 'Model Accuracy',
    lastTrained: 'Last Trained',
    retrain: 'Retrain',
    highRisk: 'High Risk',
    mediumRisk: 'Medium Risk',
    lowRisk: 'Low Risk',
    confidence: 'Confidence',
    trend: 'Trend',
    improving: 'Improving',
    declining: 'Declining',
    stable: 'Stable',
    exportReport: 'Export Report',
    configure: 'Configure',
    refresh: 'Refresh'
  }
}

export default function MLDashboard({ language, userRole }: MLDashboardProps): void {
  const [models, setModels] = useState<MLModel[]>([])
  const [performancePredictions, setPerformancePredictions] = useState<EmployeePerformancePrediction[]>([])
  const [turnoverPredictions, setTurnoverPredictions] = useState<TurnoverRiskPrediction[]>([])
  const [selectedModel, setSelectedModel] = useState<string>('')
  const [isLoading, setIsLoading] = useState(false)
  const t = translations[language]
  const isRTL = language === 'ar'

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    setIsLoading(true)
    try {
      const [modelsData, performanceData, turnoverData] = await Promise.all([
        mlService.getModels(),
        mlService.predictEmployeePerformance('emp_001'),
        mlService.analyzeTurnoverRisk()
      ])

      setModels(modelsData)
      setPerformancePredictions(Array.isArray(performanceData) ? performanceData : [performanceData])
      setTurnoverPredictions(turnoverData)
    } catch (error) {
      console.error('Failed to load ML dashboard data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleModelRetrain = async (modelId: string) => {
    try {
      await mlService.retrainModel(modelId)
      await loadDashboardData()
    } catch (error) {
      console.error('Failed to retrain model:', error)
    }
  }

  const getRiskColor = (riskLevel: string): void => {
    switch (riskLevel) {
      case 'critical':
      case 'high': return 'text-red-400'
      case 'medium': return 'text-yellow-400'
      case 'low': return 'text-green-400'
      default: return 'text-gray-400'
    }
  }

  const getTrendIcon = (trend: string): void => {
    switch (trend) {
      case 'improving': return <TrendingUp className="h-4 w-4 text-green-400" />
      case 'declining': return <TrendingDown className="h-4 w-4 text-red-400" />
      default: return <BarChart3 className="h-4 w-4 text-gray-400" />
    }
  }

  const modelPerformanceData = models.map(model => ({
    name: model.name,
    accuracy: model.accuracy * 100,
    status: model.status
  }))

  const riskDistribution = turnoverPredictions.reduce((acc, pred) => {
    acc[pred.riskLevel] = (acc[pred.riskLevel] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  const riskChartData = Object.entries(riskDistribution).map(([level, count]) => ({
    name: t[level as keyof typeof t] || level,
    value: count,
    color: level === 'high' || level === 'critical' ? '#EF4444' :
           level === 'medium' ? '#F59E0B' : '#10B981'
  }))

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-white text-xl flex items-center gap-2">
              <Brain className="h-5 w-5" />
              {t.mlDashboard}
            </CardTitle>
            <div className="flex items-center gap-3">
              <Select value={selectedModel} onValueChange={setSelectedModel}>
                <SelectTrigger className="glass-button w-48">
                  <SelectValue placeholder="Select Model" />
                </SelectTrigger>
                <SelectContent>
                  {models.map((model) => (
                    <SelectItem key={model.id} value={model.id}>
                      {model.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Button
                onClick={loadDashboardData}
                disabled={isLoading}
                className="glass-button"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                {t.refresh}
              </Button>
              <Button variant="outline" className="glass-button">
                <Download className="h-4 w-4 mr-2" />
                {t.exportReport}
              </Button>
              <Button variant="outline" className="glass-button">
                <Settings className="h-4 w-4 mr-2" />
                {t.configure}
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Model Performance Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="glass-card border-white/20">
          <CardHeader>
            <CardTitle className="text-white text-lg flex items-center gap-2">
              <Target className="h-5 w-5" />
              {t.modelPerformance}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {models.map((model) => (
                <div key={model.id} className="p-4 glass-card border-white/10 hover:border-white/30 transition-all">
                  <div className="flex items-center justify-between mb-3">
                    <div>
                      <h4 className="text-white font-medium">{model.name}</h4>
                      <p className="text-white/60 text-sm">v{model.version}</p>
                    </div>
                    <div className="text-right">
                      <Badge variant={model.status === 'active' ? 'default' : 'secondary'}>
                        {model.status}
                      </Badge>
                      <p className="text-white/60 text-xs mt-1">
                        {t.lastTrained}: {new Date(model.lastTrained).toLocaleDateString()}
                      </p>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-white/80 text-sm">{t.modelAccuracy}</span>
                      <span className="text-white font-medium">{(model.accuracy * 100).toFixed(1)}%</span>
                    </div>
                    <div className="w-full bg-white/20 rounded-full h-2">
                      <div
                        className="bg-gradient-to-r from-blue-500 to-green-500 h-2 rounded-full transition-all duration-1000"
                        style={{ width: `${model.accuracy * 100}%` }}
                      ></div>
                    </div>
                  </div>

                  <div className="flex justify-between items-center mt-3">
                    <div className="text-white/60 text-xs">
                      Features: {model.features.length}
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleModelRetrain(model.id)}
                      className="glass-button text-xs"
                    >
                      <Zap className="h-3 w-3 mr-1" />
                      {t.retrain}
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardHeader>
            <CardTitle className="text-white text-lg">{t.turnoverRisk}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={riskChartData}
                    cx="50%"
                    cy="50%"
                    innerRadius={60}
                    outerRadius={100}
                    paddingAngle={5}
                    dataKey="value"
                  >
                    {riskChartData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip
                    contentStyle={{
                      backgroundColor: 'rgba(0,0,0,0.8)',
                      border: '1px solid rgba(255,255,255,0.2)',
                      borderRadius: '8px',
                      color: 'white'
                    }}
                  />
                </PieChart>
              </ResponsiveContainer>
            </div>
            <div className="grid grid-cols-2 gap-4 mt-4">
              {riskChartData.map((item, index) => (
                <div key={index} className="flex items-center gap-2">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: item.color }}
                  ></div>
                  <span className="text-white/80 text-sm">{item.name}: {item.value}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Employee Performance Predictions */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white text-lg flex items-center gap-2">
            <Users className="h-5 w-5" />
            {t.employeePerformance}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {performancePredictions.slice(0, 6).map((prediction, index) => (
              <div key={index} className="p-4 glass-card border-white/10 hover:border-white/30 transition-all">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs font-medium">
                        {prediction.employeeId.slice(-3)}
                      </span>
                    </div>
                    {getTrendIcon(prediction.trend)}
                  </div>
                  <Badge variant="outline" className="text-xs">
                    {t.confidence}: {(prediction.confidence * 100).toFixed(0)}%
                  </Badge>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-white/80 text-sm">Current Score</span>
                    <span className="text-white font-medium">{prediction.currentScore}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-white/80 text-sm">Predicted Score</span>
                    <span className="text-green-400 font-medium">{prediction.predictedScore.toFixed(1)}</span>
                  </div>
                  <div className="w-full bg-white/20 rounded-full h-2">
                    <div
                      className="bg-gradient-to-r from-blue-500 to-green-500 h-2 rounded-full transition-all duration-1000"
                      style={{ width: `${prediction.predictedScore}%` }}
                    ></div>
                  </div>
                </div>

                <div className="mt-3">
                  <p className="text-white/60 text-xs mb-1">Top Recommendations:</p>
                  <ul className="text-white/70 text-xs space-y-1">
                    {prediction.recommendations.slice(0, 2).map((rec, idx) => (
                      <li key={idx} className="truncate">• {rec}</li>
                    ))}
                  </ul>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* High-Risk Employees */}
      <Card className="glass-card border-white/20">
        <CardHeader>
          <CardTitle className="text-white text-lg flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            High-Risk Employees
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {turnoverPredictions
              .filter(pred => pred.riskLevel === 'high' || pred.riskLevel === 'critical')
              .slice(0, 5)
              .map((prediction, index) => (
                <div key={index} className="p-4 glass-card border-white/10 hover:border-white/30 transition-all">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-red-500 to-orange-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-sm font-medium">
                          {prediction.employeeId.slice(-3)}
                        </span>
                      </div>
                      <div>
                        <h4 className="text-white font-medium">Employee {prediction.employeeId}</h4>
                        <p className="text-white/60 text-sm">
                          Risk: {(prediction.probability * 100).toFixed(0)}% in {prediction.timeframe}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <Badge variant="destructive" className="mb-2">
                        {prediction.riskLevel}
                      </Badge>
                      <p className="text-white/60 text-xs">
                        {prediction.keyFactors.length} risk factors
                      </p>
                    </div>
                  </div>

                  <div className="mt-3 grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p className="text-white/60 text-xs mb-1">Key Risk Factors:</p>
                      <ul className="text-white/70 text-xs space-y-1">
                        {prediction.keyFactors.slice(0, 3).map((factor, idx) => (
                          <li key={idx}>• {factor}</li>
                        ))}
                      </ul>
                    </div>
                    <div>
                      <p className="text-white/60 text-xs mb-1">Retention Strategies:</p>
                      <ul className="text-white/70 text-xs space-y-1">
                        {prediction.retentionStrategies.slice(0, 3).map((strategy, idx) => (
                          <li key={idx}>• {strategy}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
