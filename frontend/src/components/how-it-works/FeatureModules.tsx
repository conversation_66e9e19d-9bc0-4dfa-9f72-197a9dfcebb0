import React from 'react';
import { useState } from 'react'
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import {
  Users,
  Building,
  BarChart3,
  DollarSign,
  Calendar,
  FileText,
  Package,
  MessageSquare,
  Shield,
  Zap,
  TrendingUp,
  Settings,
  Globe,
  Brain,
  Target,
  Headphones,
  ShoppingCart,
  Truck,
  Award,
  BookOpen,
  Search,
  Bell,
  Lock,
  Database,
  Workflow,
  ChevronRight
} from 'lucide-react'

interface FeatureModulesProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    title: 'وحدات النظام',
    subtitle: 'أكثر من 50 وحدة متكاملة لإدارة شاملة',
    description: 'نظام نمو يوفر جميع الأدوات التي تحتاجها لإدارة مؤسستك بكفاءة عالية',

    // Categories
    coreManagement: 'الإدارة الأساسية',
    hrManagement: 'إدارة الموارد البشرية',
    financialManagement: 'الإدارة المالية',
    projectManagement: 'إدارة المشاريع',
    salesCRM: 'المبيعات وإدارة العملاء',
    operationsSupply: 'العمليات وسلسلة التوريد',
    communicationCollab: 'التواصل والتعاون',
    analyticsReporting: 'التحليلات والتقارير',
    securityCompliance: 'الأمان والامتثال',
    aiAutomation: 'الذكاء الاصطناعي والأتمتة',

    viewDetails: 'عرض التفاصيل',
    features: 'الميزات',
    benefits: 'الفوائد'
  },
  en: {
    title: 'Feature Modules',
    subtitle: '50+ Integrated Modules for Comprehensive Management',
    description: 'Numu provides all the tools you need to manage your organization with high efficiency',

    // Categories
    coreManagement: 'Core Management',
    hrManagement: 'HR Management',
    financialManagement: 'Financial Management',
    projectManagement: 'Project Management',
    salesCRM: 'Sales & CRM',
    operationsSupply: 'Operations & Supply Chain',
    communicationCollab: 'Communication & Collaboration',
    analyticsReporting: 'Analytics & Reporting',
    securityCompliance: 'Security & Compliance',
    aiAutomation: 'AI & Automation',

    viewDetails: 'View Details',
    features: 'Features',
    benefits: 'Benefits'
  }
}

export default function FeatureModules({ language }: FeatureModulesProps): void {
  const [selectedCategory, setSelectedCategory] = useState('coreManagement')
  const t = translations[language]
  const isRTL = language === 'ar'

  const moduleCategories = {
    coreManagement: {
      title: t.coreManagement,
      icon: Building,
      color: 'from-blue-500 to-cyan-500',
      modules: [
        {
          name: language === 'ar' ? 'إدارة الموظفين' : 'Employee Management',
          icon: Users,
          features: [
            language === 'ar' ? 'ملفات الموظفين الشاملة' : 'Comprehensive employee profiles',
            language === 'ar' ? 'إدارة الأدوار والصلاحيات' : 'Role and permission management',
            language === 'ar' ? 'تتبع الأداء' : 'Performance tracking'
          ]
        },
        {
          name: language === 'ar' ? 'إدارة الأقسام' : 'Department Management',
          icon: Building,
          features: [
            language === 'ar' ? 'هيكل تنظيمي مرن' : 'Flexible organizational structure',
            language === 'ar' ? 'إدارة التسلسل الهرمي' : 'Hierarchy management',
            language === 'ar' ? 'تقارير الأقسام' : 'Department reports'
          ]
        },
        {
          name: language === 'ar' ? 'إدارة المستخدمين' : 'User Management',
          icon: Shield,
          features: [
            language === 'ar' ? 'إدارة الحسابات' : 'Account management',
            language === 'ar' ? 'أمان متقدم' : 'Advanced security',
            language === 'ar' ? 'تسجيل الدخول الموحد' : 'Single sign-on'
          ]
        }
      ]
    },
    hrManagement: {
      title: t.hrManagement,
      icon: Users,
      color: 'from-green-500 to-emerald-500',
      modules: [
        {
          name: language === 'ar' ? 'إدارة الإجازات' : 'Leave Management',
          icon: Calendar,
          features: [
            language === 'ar' ? 'طلبات الإجازات' : 'Leave requests',
            language === 'ar' ? 'أنواع الإجازات المختلفة' : 'Multiple leave types',
            language === 'ar' ? 'موافقات تلقائية' : 'Automated approvals'
          ]
        },
        {
          name: language === 'ar' ? 'إدارة الحضور' : 'Attendance Management',
          icon: BarChart3,
          features: [
            language === 'ar' ? 'تتبع الحضور والانصراف' : 'Clock in/out tracking',
            language === 'ar' ? 'تقارير الحضور' : 'Attendance reports',
            language === 'ar' ? 'إدارة المناوبات' : 'Shift management'
          ]
        },
        {
          name: language === 'ar' ? 'إدارة الرواتب' : 'Payroll Management',
          icon: DollarSign,
          features: [
            language === 'ar' ? 'حساب الرواتب التلقائي' : 'Automated payroll calculation',
            language === 'ar' ? 'إدارة البدلات' : 'Allowance management',
            language === 'ar' ? 'تقارير الرواتب' : 'Payroll reports'
          ]
        }
      ]
    },
    financialManagement: {
      title: t.financialManagement,
      icon: DollarSign,
      color: 'from-yellow-500 to-orange-500',
      modules: [
        {
          name: language === 'ar' ? 'إدارة الميزانيات' : 'Budget Management',
          icon: BarChart3,
          features: [
            language === 'ar' ? 'تخطيط الميزانية' : 'Budget planning',
            language === 'ar' ? 'تتبع المصروفات' : 'Expense tracking',
            language === 'ar' ? 'تحليل الانحرافات' : 'Variance analysis'
          ]
        },
        {
          name: language === 'ar' ? 'إدارة المصروفات' : 'Expense Management',
          icon: FileText,
          features: [
            language === 'ar' ? 'تسجيل المصروفات' : 'Expense recording',
            language === 'ar' ? 'موافقات المصروفات' : 'Expense approvals',
            language === 'ar' ? 'تقارير مالية' : 'Financial reports'
          ]
        },
        {
          name: language === 'ar' ? 'إدارة الأصول' : 'Asset Management',
          icon: Package,
          features: [
            language === 'ar' ? 'تتبع الأصول' : 'Asset tracking',
            language === 'ar' ? 'جدولة الصيانة' : 'Maintenance scheduling',
            language === 'ar' ? 'إهلاك الأصول' : 'Asset depreciation'
          ]
        }
      ]
    },
    projectManagement: {
      title: t.projectManagement,
      icon: Workflow,
      color: 'from-purple-500 to-pink-500',
      modules: [
        {
          name: language === 'ar' ? 'إدارة المشاريع' : 'Project Management',
          icon: Workflow,
          features: [
            language === 'ar' ? 'تخطيط المشاريع' : 'Project planning',
            language === 'ar' ? 'تتبع التقدم' : 'Progress tracking',
            language === 'ar' ? 'إدارة الموارد' : 'Resource management'
          ]
        },
        {
          name: language === 'ar' ? 'إدارة المهام' : 'Task Management',
          icon: Target,
          features: [
            language === 'ar' ? 'تعيين المهام' : 'Task assignment',
            language === 'ar' ? 'تتبع الحالة' : 'Status tracking',
            language === 'ar' ? 'إدارة الأولويات' : 'Priority management'
          ]
        },
        {
          name: language === 'ar' ? 'تقارير المشاريع' : 'Project Reports',
          icon: BarChart3,
          features: [
            language === 'ar' ? 'تقارير الأداء' : 'Performance reports',
            language === 'ar' ? 'تحليل التكاليف' : 'Cost analysis',
            language === 'ar' ? 'تقارير الجودة' : 'Quality reports'
          ]
        }
      ]
    },
    salesCRM: {
      title: t.salesCRM,
      icon: ShoppingCart,
      color: 'from-red-500 to-rose-500',
      modules: [
        {
          name: language === 'ar' ? 'إدارة العملاء' : 'Customer Management',
          icon: Users,
          features: [
            language === 'ar' ? 'ملفات العملاء' : 'Customer profiles',
            language === 'ar' ? 'تاريخ التفاعلات' : 'Interaction history',
            language === 'ar' ? 'تصنيف العملاء' : 'Customer segmentation'
          ]
        },
        {
          name: language === 'ar' ? 'إدارة المبيعات' : 'Sales Management',
          icon: TrendingUp,
          features: [
            language === 'ar' ? 'خط أنابيب المبيعات' : 'Sales pipeline',
            language === 'ar' ? 'إدارة العروض' : 'Quote management',
            language === 'ar' ? 'تتبع الفرص' : 'Opportunity tracking'
          ]
        },
        {
          name: language === 'ar' ? 'إدارة المنتجات' : 'Product Management',
          icon: Package,
          features: [
            language === 'ar' ? 'كتالوج المنتجات' : 'Product catalog',
            language === 'ar' ? 'إدارة المخزون' : 'Inventory management',
            language === 'ar' ? 'تسعير المنتجات' : 'Product pricing'
          ]
        }
      ]
    }
  }

  const categories = Object.keys(moduleCategories) as Array<keyof typeof moduleCategories>

  return (
    <div className={`space-y-10 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="text-center animate-fade-in-up">
        <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-white via-white to-white/80 bg-clip-text text-transparent mb-6">{t.title}</h2>
        <p className="text-xl text-white/90 mb-4">{t.subtitle}</p>
        <p className="text-white/70 max-w-3xl mx-auto text-lg">{t.description}</p>
      </div>

      {/* Category Selector */}
      <div className="flex flex-wrap justify-center gap-3 animate-fade-in-up animation-delay-200">
        {categories.map((categoryKey) => {
          const category = moduleCategories[categoryKey]
          const Icon = category.icon
          return (
            <Button
              key={categoryKey}
              onClick={() => setSelectedCategory(categoryKey)}
              className={`glass-button transition-all duration-300 ${
                selectedCategory === categoryKey
                  ? 'bg-gradient-to-r from-blue-500 to-purple-500 border-0 text-white shadow-lg scale-105'
                  : 'border-white/20 hover:border-white/40 hover:bg-white/10'
              }`}
            >
              <Icon className="h-4 w-4 mr-2" />
              {category.title}
            </Button>
          )
        })}
      </div>

      {/* Selected Category Modules */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 animate-fade-in-up animation-delay-400">
        {moduleCategories[selectedCategory as keyof typeof moduleCategories].modules.map((module, index) => {
          const Icon = module.icon
          return (
            <Card key={index} className="glass-card border-white/20 hover:border-white/40 transition-all duration-300 group hover:scale-105">
              <CardHeader>
                <div className="flex items-center gap-3">
                  <div className={`w-12 h-12 bg-gradient-to-r ${moduleCategories[selectedCategory as keyof typeof moduleCategories].color} rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
                    <Icon className="h-6 w-6 text-white" />
                  </div>
                  <CardTitle className="text-white text-lg font-semibold">{module.name}</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 mb-6">
                  {module.features.map((feature, featureIndex) => (
                    <div key={featureIndex} className="flex items-start gap-3">
                      <ChevronRight className="h-4 w-4 text-blue-400 mt-0.5 flex-shrink-0" />
                      <span className="text-white/80 text-sm leading-relaxed">{feature}</span>
                    </div>
                  ))}
                </div>
                <Button className="w-full glass-button border-white/30 hover:border-white/50 hover:bg-white/10 transition-all duration-300">
                  {t.viewDetails}
                </Button>
              </CardContent>
            </Card>
          )
        })}
      </div>
    </div>
  )
}
