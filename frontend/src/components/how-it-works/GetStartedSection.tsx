import { useState } from 'react'
import { <PERSON> } from 'react-router-dom'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import {
  ArrowRight,
  CheckCircle,
  Clock,
  Users,
  Zap,
  Star,
  Play,
  Download,
  BookOpen,
  MessageSquare,
  Phone,
  Mail,
  Calendar,
  Gift,
  Target,
  TrendingUp,
  Shield,
  Globe,
  Settings
} from 'lucide-react'

interface GetStartedSectionProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    title: 'ابدأ رحلتك مع نمو',
    subtitle: 'انضم إلى آلاف الشركات التي تثق في نمو',
    description: 'احصل على نظام إدارة مؤسسات شامل يساعدك على تحقيق أهدافك وزيادة الإنتاجية',

    // Getting Started Steps
    gettingStarted: 'خطوات البدء',
    step1Title: 'إنشاء حساب مجاني',
    step1Desc: 'سجل حساباً مجانياً واحصل على تجربة 30 يوماً',
    step2Title: 'إعداد مؤسستك',
    step2Desc: 'أضف معلومات مؤسستك والموظفين',
    step3Title: 'بدء الاستخدام',
    step3Desc: 'ابدأ في إدارة عملياتك بكفاءة',

    // Pricing Plans
    pricingPlans: 'خطط الأسعار',
    freeTrial: 'تجربة مجانية',
    freeTrialDesc: '30 يوماً مجاناً - جميع الميزات',
    basicPlan: 'الخطة الأساسية',
    basicPlanDesc: 'للشركات الصغيرة',
    professionalPlan: 'الخطة المهنية',
    professionalPlanDesc: 'للشركات المتوسطة',
    enterprisePlan: 'خطة المؤسسات',
    enterprisePlanDesc: 'للشركات الكبيرة',

    // Features included
    featuresIncluded: 'الميزات المشمولة',
    unlimitedUsers: 'مستخدمين غير محدودين',
    allModules: 'جميع الوحدات',
    support247: 'دعم 24/7',
    customization: 'تخصيص كامل',
    training: 'تدريب مجاني',
    migration: 'نقل البيانات',

    // Call to Action
    startFreeTrial: 'ابدأ التجربة المجانية',
    contactSales: 'تواصل مع المبيعات',
    watchDemo: 'شاهد العرض التوضيحي',
    downloadBrochure: 'تحميل الكتيب',

    // Contact Information
    contactUs: 'تواصل معنا',
    phone: 'الهاتف',
    email: 'البريد الإلكتروني',
    schedule: 'حدد موعد',

    // Success Stories
    successStories: 'قصص النجاح',
    customerTestimonial: 'آراء العملاء',

    // Resources
    resources: 'الموارد',
    documentation: 'الوثائق',
    tutorials: 'الدروس التعليمية',
    webinars: 'الندوات الإلكترونية',
    community: 'المجتمع',

    // Benefits
    benefits: 'الفوائد',
    increaseProductivity: 'زيادة الإنتاجية بنسبة 40%',
    reduceCosts: 'تقليل التكاليف بنسبة 30%',
    improveEfficiency: 'تحسين الكفاءة بنسبة 50%',
    enhanceSecurity: 'تعزيز الأمان بنسبة 95%',

    // Form
    firstName: 'الاسم الأول',
    lastName: 'الاسم الأخير',
    companyName: 'اسم الشركة',
    emailAddress: 'البريد الإلكتروني',
    phoneNumber: 'رقم الهاتف',
    submit: 'إرسال',

    // Guarantees
    guarantees: 'ضماناتنا',
    moneyBack: 'ضمان استرداد الأموال',
    dataProtection: 'حماية البيانات',
    uptime: 'ضمان التشغيل 99.9%',
    support: 'دعم مجاني'
  },
  en: {
    title: 'Start Your Journey with Numu',
    subtitle: 'Join thousands of companies that trust Numu',
    description: 'Get a comprehensive enterprise management system that helps you achieve your goals and increase productivity',

    // Getting Started Steps
    gettingStarted: 'Getting Started Steps',
    step1Title: 'Create Free Account',
    step1Desc: 'Register a free account and get 30-day trial',
    step2Title: 'Setup Your Organization',
    step2Desc: 'Add your organization and employee information',
    step3Title: 'Start Using',
    step3Desc: 'Begin managing your operations efficiently',

    // Pricing Plans
    pricingPlans: 'Pricing Plans',
    freeTrial: 'Free Trial',
    freeTrialDesc: '30 days free - All features',
    basicPlan: 'Basic Plan',
    basicPlanDesc: 'For small businesses',
    professionalPlan: 'Professional Plan',
    professionalPlanDesc: 'For medium businesses',
    enterprisePlan: 'Enterprise Plan',
    enterprisePlanDesc: 'For large organizations',

    // Features included
    featuresIncluded: 'Features Included',
    unlimitedUsers: 'Unlimited Users',
    allModules: 'All Modules',
    support247: '24/7 Support',
    customization: 'Full Customization',
    training: 'Free Training',
    migration: 'Data Migration',

    // Call to Action
    startFreeTrial: 'Start Free Trial',
    contactSales: 'Contact Sales',
    watchDemo: 'Watch Demo',
    downloadBrochure: 'Download Brochure',

    // Contact Information
    contactUs: 'Contact Us',
    phone: 'Phone',
    email: 'Email',
    schedule: 'Schedule Meeting',

    // Success Stories
    successStories: 'Success Stories',
    customerTestimonial: 'Customer Testimonials',

    // Resources
    resources: 'Resources',
    documentation: 'Documentation',
    tutorials: 'Tutorials',
    webinars: 'Webinars',
    community: 'Community',

    // Benefits
    benefits: 'Benefits',
    increaseProductivity: 'Increase productivity by 40%',
    reduceCosts: 'Reduce costs by 30%',
    improveEfficiency: 'Improve efficiency by 50%',
    enhanceSecurity: 'Enhance security by 95%',

    // Form
    firstName: 'First Name',
    lastName: 'Last Name',
    companyName: 'Company Name',
    emailAddress: 'Email Address',
    phoneNumber: 'Phone Number',
    submit: 'Submit',

    // Guarantees
    guarantees: 'Our Guarantees',
    moneyBack: 'Money Back Guarantee',
    dataProtection: 'Data Protection',
    uptime: '99.9% Uptime Guarantee',
    support: 'Free Support'
  }
}

export default function GetStartedSection({ language }: GetStartedSectionProps): void {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    companyName: '',
    email: '',
    phone: ''
  })

  const t = translations[language]
  const isRTL = language === 'ar'

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = (e: React.FormEvent): void => {
    e.preventDefault()
    // Handle form submission
    console.log('Form submitted:', formData)
  }

  const gettingStartedSteps = [
    {
      title: t.step1Title,
      description: t.step1Desc,
      icon: Users,
      color: 'from-blue-500 to-cyan-500'
    },
    {
      title: t.step2Title,
      description: t.step2Desc,
      icon: Settings,
      color: 'from-green-500 to-emerald-500'
    },
    {
      title: t.step3Title,
      description: t.step3Desc,
      icon: Zap,
      color: 'from-purple-500 to-pink-500'
    }
  ]

  const pricingPlans = [
    {
      name: t.freeTrial,
      description: t.freeTrialDesc,
      price: language === 'ar' ? 'مجاني' : 'Free',
      period: '30 ' + (language === 'ar' ? 'يوم' : 'days'),
      features: [
        t.allModules,
        t.unlimitedUsers,
        t.support247,
        t.training
      ],
      popular: false,
      cta: t.startFreeTrial
    },
    {
      name: t.basicPlan,
      description: t.basicPlanDesc,
      price: '$29',
      period: language === 'ar' ? 'شهرياً' : '/month',
      features: [
        t.allModules,
        '50 ' + (language === 'ar' ? 'مستخدم' : 'users'),
        t.support247,
        t.training
      ],
      popular: false,
      cta: t.startFreeTrial
    },
    {
      name: t.professionalPlan,
      description: t.professionalPlanDesc,
      price: '$79',
      period: language === 'ar' ? 'شهرياً' : '/month',
      features: [
        t.allModules,
        t.unlimitedUsers,
        t.support247,
        t.customization,
        t.training
      ],
      popular: true,
      cta: t.startFreeTrial
    },
    {
      name: t.enterprisePlan,
      description: t.enterprisePlanDesc,
      price: language === 'ar' ? 'مخصص' : 'Custom',
      period: '',
      features: [
        t.allModules,
        t.unlimitedUsers,
        t.support247,
        t.customization,
        t.training,
        t.migration
      ],
      popular: false,
      cta: t.contactSales
    }
  ]

  const benefits = [
    {
      title: t.increaseProductivity,
      icon: TrendingUp,
      color: 'from-green-500 to-emerald-500'
    },
    {
      title: t.reduceCosts,
      icon: Target,
      color: 'from-blue-500 to-cyan-500'
    },
    {
      title: t.improveEfficiency,
      icon: Zap,
      color: 'from-purple-500 to-pink-500'
    },
    {
      title: t.enhanceSecurity,
      icon: Shield,
      color: 'from-red-500 to-orange-500'
    }
  ]

  const guarantees = [
    {
      title: t.moneyBack,
      description: language === 'ar' ? '30 يوم ضمان استرداد' : '30-day money back',
      icon: Gift
    },
    {
      title: t.dataProtection,
      description: language === 'ar' ? 'حماية كاملة للبيانات' : 'Complete data protection',
      icon: Shield
    },
    {
      title: t.uptime,
      description: language === 'ar' ? 'ضمان التشغيل المستمر' : 'Continuous operation guarantee',
      icon: Clock
    },
    {
      title: t.support,
      description: language === 'ar' ? 'دعم فني مجاني' : 'Free technical support',
      icon: MessageSquare
    }
  ]

  return (
    <div className={`space-y-12 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="text-center">
        <h2 className="text-4xl font-bold text-white mb-4">{t.title}</h2>
        <p className="text-xl text-white/80 mb-2">{t.subtitle}</p>
        <p className="text-white/70 max-w-3xl mx-auto">{t.description}</p>
      </div>

      {/* Benefits */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {benefits.map((benefit, index) => {
          const Icon = benefit.icon
          return (
            <Card key={index} className="glass-card border-white/20 hover:border-white/40 transition-all duration-300 text-center">
              <CardContent className="p-6">
                <div className={`w-12 h-12 bg-gradient-to-r ${benefit.color} rounded-lg flex items-center justify-center mx-auto mb-4`}>
                  <Icon className="h-6 w-6 text-white" />
                </div>
                <h4 className="text-white font-semibold">{benefit.title}</h4>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {/* Getting Started Steps */}
      <div>
        <h3 className="text-3xl font-bold text-white text-center mb-8">{t.gettingStarted}</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {gettingStartedSteps.map((step, index) => {
            const Icon = step.icon
            return (
              <Card key={index} className="glass-card border-white/20 hover:border-white/40 transition-all duration-300 text-center group">
                <CardContent className="p-8">
                  <div className={`w-16 h-16 bg-gradient-to-r ${step.color} rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300`}>
                    <Icon className="h-8 w-8 text-white" />
                  </div>
                  <h4 className="text-white font-semibold text-xl mb-3">{step.title}</h4>
                  <p className="text-white/70">{step.description}</p>
                </CardContent>
              </Card>
            )
          })}
        </div>
      </div>

      {/* Pricing Plans */}
      <div>
        <h3 className="text-3xl font-bold text-white text-center mb-8">{t.pricingPlans}</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {pricingPlans.map((plan, index) => (
            <Card key={index} className={`glass-card transition-all duration-300 ${
              plan.popular
                ? 'border-blue-500/50 bg-blue-500/10 scale-105'
                : 'border-white/20 hover:border-white/40'
            }`}>
              {plan.popular && (
                <div className="bg-blue-500 text-white text-center py-2 text-sm font-semibold rounded-t-lg">
                  {language === 'ar' ? 'الأكثر شعبية' : 'Most Popular'}
                </div>
              )}
              <CardHeader className="text-center">
                <CardTitle className="text-white text-xl">{plan.name}</CardTitle>
                <p className="text-white/70 text-sm">{plan.description}</p>
                <div className="text-3xl font-bold text-white mt-4">
                  {plan.price}
                  {plan.period && <span className="text-lg text-white/70">{plan.period}</span>}
                </div>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 mb-6">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-400" />
                      <span className="text-white/80 text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>
                <Button className="w-full glass-button border-white/30 hover:border-white/50">
                  {plan.cta}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Contact Form */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <Card className="glass-card border-white/20">
          <CardHeader>
            <CardTitle className="text-white text-xl">{t.startFreeTrial}</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Input
                  name="firstName"
                  placeholder={t.firstName}
                  value={formData.firstName}
                  onChange={handleInputChange}
                  className="glass-input"
                />
                <Input
                  name="lastName"
                  placeholder={t.lastName}
                  value={formData.lastName}
                  onChange={handleInputChange}
                  className="glass-input"
                />
              </div>
              <Input
                name="companyName"
                placeholder={t.companyName}
                value={formData.companyName}
                onChange={handleInputChange}
                className="glass-input"
              />
              <Input
                name="email"
                type="email"
                placeholder={t.emailAddress}
                value={formData.email}
                onChange={handleInputChange}
                className="glass-input"
              />
              <Input
                name="phone"
                placeholder={t.phoneNumber}
                value={formData.phone}
                onChange={handleInputChange}
                className="glass-input"
              />
              <Button type="submit" className="w-full bg-blue-500 hover:bg-blue-600 text-white">
                {t.submit}
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </form>
          </CardContent>
        </Card>

        <div className="space-y-6">
          {/* Contact Information */}
          <Card className="glass-card border-white/20">
            <CardHeader>
              <CardTitle className="text-white text-xl">{t.contactUs}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-3">
                <Phone className="h-5 w-5 text-white/70" />
                <span className="text-white">+****************</span>
              </div>
              <div className="flex items-center gap-3">
                <Mail className="h-5 w-5 text-white/70" />
                <span className="text-white"><EMAIL></span>
              </div>
              <Button className="w-full glass-button border-white/30 hover:border-white/50">
                <Calendar className="h-4 w-4 mr-2" />
                {t.schedule}
              </Button>
            </CardContent>
          </Card>

          {/* Guarantees */}
          <Card className="glass-card border-white/20">
            <CardHeader>
              <CardTitle className="text-white text-xl">{t.guarantees}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                {guarantees.map((guarantee, index) => {
                  const Icon = guarantee.icon
                  return (
                    <div key={index} className="text-center">
                      <Icon className="h-8 w-8 text-white/70 mx-auto mb-2" />
                      <h5 className="text-white font-semibold text-sm mb-1">{guarantee.title}</h5>
                      <p className="text-white/60 text-xs">{guarantee.description}</p>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Call to Action */}
      <div className="text-center">
        <div className="flex flex-wrap justify-center gap-4">
          <Link to="/login">
            <Button className="bg-blue-500 hover:bg-blue-600 text-white px-8 py-3">
              <Play className="h-4 w-4 mr-2" />
              {t.startFreeTrial}
            </Button>
          </Link>
          <Button variant="ghost" className="glass-button border-white/30 hover:border-white/50 px-8 py-3">
            <Download className="h-4 w-4 mr-2" />
            {t.downloadBrochure}
          </Button>
          <Button variant="ghost" className="glass-button border-white/30 hover:border-white/50 px-8 py-3">
            <Play className="h-4 w-4 mr-2" />
            {t.watchDemo}
          </Button>
        </div>
      </div>
    </div>
  )
}
