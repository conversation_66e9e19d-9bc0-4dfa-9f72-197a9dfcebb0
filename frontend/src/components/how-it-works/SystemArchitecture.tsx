import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from '../ui/card'
import {
  Server,
  Database,
  Cloud,
  Shield,
  Zap,
  Globe,
  Users,
  Building,
  BarChart3,
  Cog,
  Lock,
  Smartphone
} from 'lucide-react'

interface SystemArchitectureProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    title: 'هيكل النظام',
    subtitle: 'بنية تقنية متقدمة وقابلة للتوسع',
    description: 'نظام نمو مبني على أحدث التقنيات لضمان الأداء العالي والأمان والموثوقية',

    // Architecture Layers
    presentationLayer: 'طبقة العرض',
    presentationDesc: 'واجهة مستخدم حديثة مع React و TypeScript',

    applicationLayer: 'طبقة التطبيق',
    applicationDesc: 'منطق الأعمال والخدمات المصغرة',

    dataLayer: 'طبقة البيانات',
    dataDesc: 'قاعدة بيانات PostgreSQL مع Redis للتخزين المؤقت',

    infrastructureLayer: 'طبقة البنية التحتية',
    infrastructureDesc: 'خدمات سحابية قابلة للتوسع مع Docker',

    // Key Features
    keyFeatures: 'الميزات الرئيسية',
    multiTenant: 'متعدد المستأجرين',
    multiTenantDesc: 'دعم عدة مؤسسات في نفس النظام',

    realTime: 'الوقت الفعلي',
    realTimeDesc: 'تحديثات فورية وإشعارات مباشرة',

    scalable: 'قابل للتوسع',
    scalableDesc: 'يدعم نمو مؤسستك بلا حدود',

    secure: 'آمن',
    secureDesc: 'تشفير متقدم وحماية البيانات',

    mobile: 'متوافق مع الجوال',
    mobileDesc: 'تطبيق ويب متجاوب يعمل على جميع الأجهزة',

    api: 'واجهات برمجية',
    apiDesc: 'REST APIs للتكامل مع الأنظمة الأخرى'
  },
  en: {
    title: 'System Architecture',
    subtitle: 'Advanced and Scalable Technical Architecture',
    description: 'Numu is built on cutting-edge technologies to ensure high performance, security, and reliability',

    // Architecture Layers
    presentationLayer: 'Presentation Layer',
    presentationDesc: 'Modern UI with React & TypeScript',

    applicationLayer: 'Application Layer',
    applicationDesc: 'Business logic and microservices',

    dataLayer: 'Data Layer',
    dataDesc: 'PostgreSQL database with Redis caching',

    infrastructureLayer: 'Infrastructure Layer',
    infrastructureDesc: 'Scalable cloud services with Docker',

    // Key Features
    keyFeatures: 'Key Features',
    multiTenant: 'Multi-Tenant',
    multiTenantDesc: 'Support multiple organizations in one system',

    realTime: 'Real-Time',
    realTimeDesc: 'Instant updates and live notifications',

    scalable: 'Scalable',
    scalableDesc: 'Supports unlimited organizational growth',

    secure: 'Secure',
    secureDesc: 'Advanced encryption and data protection',

    mobile: 'Mobile-Friendly',
    mobileDesc: 'Responsive web app works on all devices',

    api: 'API-First',
    apiDesc: 'REST APIs for integration with other systems'
  }
}

export default function SystemArchitecture({ language }: SystemArchitectureProps): void {
  const t = translations[language]
  const isRTL = language === 'ar'

  const architectureLayers = [
    {
      title: t.presentationLayer,
      description: t.presentationDesc,
      icon: Smartphone,
      color: 'from-blue-500 to-cyan-500',
      technologies: ['React', 'TypeScript', 'Tailwind CSS', 'Vite']
    },
    {
      title: t.applicationLayer,
      description: t.applicationDesc,
      icon: Cog,
      color: 'from-purple-500 to-pink-500',
      technologies: ['Django', 'Python', 'REST APIs', 'WebSockets']
    },
    {
      title: t.dataLayer,
      description: t.dataDesc,
      icon: Database,
      color: 'from-green-500 to-emerald-500',
      technologies: ['PostgreSQL', 'Redis', 'Elasticsearch', 'MinIO']
    },
    {
      title: t.infrastructureLayer,
      description: t.infrastructureDesc,
      icon: Cloud,
      color: 'from-orange-500 to-red-500',
      technologies: ['Docker', 'Kubernetes', 'AWS/Azure', 'Nginx']
    }
  ]

  const keyFeatures = [
    {
      title: t.multiTenant,
      description: t.multiTenantDesc,
      icon: Building,
      color: 'from-blue-500 to-purple-500'
    },
    {
      title: t.realTime,
      description: t.realTimeDesc,
      icon: Zap,
      color: 'from-yellow-500 to-orange-500'
    },
    {
      title: t.scalable,
      description: t.scalableDesc,
      icon: BarChart3,
      color: 'from-green-500 to-blue-500'
    },
    {
      title: t.secure,
      description: t.secureDesc,
      icon: Shield,
      color: 'from-red-500 to-pink-500'
    },
    {
      title: t.mobile,
      description: t.mobileDesc,
      icon: Smartphone,
      color: 'from-purple-500 to-indigo-500'
    },
    {
      title: t.api,
      description: t.apiDesc,
      icon: Globe,
      color: 'from-cyan-500 to-teal-500'
    }
  ]

  return (
    <div className={`space-y-12 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="text-center animate-fade-in-up">
        <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-white via-white to-white/80 bg-clip-text text-transparent mb-6">{t.title}</h2>
        <p className="text-xl text-white/90 mb-4">{t.subtitle}</p>
        <p className="text-white/70 max-w-3xl mx-auto text-lg">{t.description}</p>
      </div>

      {/* Architecture Layers */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        {architectureLayers.map((layer, index) => {
          const Icon = layer.icon
          return (
            <div key={index} className="glass-card p-8 rounded-2xl border border-white/20 text-center group hover:border-white/40 transition-all duration-300 animate-fade-in-up hover-scale" style={{animationDelay: `${index * 200}ms`}}>
              <div className={`w-16 h-16 bg-gradient-to-r ${layer.color} rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 animate-bounce-slow`}>
                <Icon className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-white mb-4">{layer.title}</h3>
              <p className="text-white/70 leading-relaxed mb-4">{layer.description}</p>
              <div className="flex flex-wrap gap-2 justify-center">
                {layer.technologies.map((tech, techIndex) => (
                  <span
                    key={techIndex}
                    className="px-3 py-1 bg-white/10 text-white/90 text-xs rounded-full border border-white/20 hover:bg-white/20 transition-colors duration-200"
                  >
                    {tech}
                  </span>
                ))}
              </div>
            </div>
          )
        })}
      </div>

      {/* Key Features */}
      <div className="animate-fade-in-up animation-delay-400">
        <h3 className="text-3xl md:text-4xl font-bold text-white text-center mb-10">{t.keyFeatures}</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {keyFeatures.map((feature, index) => {
            const Icon = feature.icon
            return (
              <div key={index} className="glass-card p-8 rounded-2xl border border-white/20 text-center group hover:border-white/40 transition-all duration-300 animate-fade-in-up hover-scale" style={{animationDelay: `${(index + 4) * 200}ms`}}>
                <div className={`w-16 h-16 bg-gradient-to-r ${feature.color} rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 animate-rotate-slow`}>
                  <Icon className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-white mb-4">{feature.title}</h3>
                <p className="text-white/70 leading-relaxed">{feature.description}</p>
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )
}
