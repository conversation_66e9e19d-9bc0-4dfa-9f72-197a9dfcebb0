import React from 'react';
import { useState } from 'react'
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '../ui/card'
import { Button } from '../ui/button'
import {
  Play,
  Pause,
  RotateCcw,
  ArrowRight,
  CheckCircle,
  Clock,
  AlertCircle,
  User,
  FileText,
  Send,
  Eye,
  ThumbsUp,
  ThumbsDown,
  Calendar,
  DollarSign,
  Package,
  Users
} from 'lucide-react'

interface WorkflowDemoProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    title: 'عرض سير العمل',
    subtitle: 'شاهد كيف تعمل العمليات في النظام',
    description: 'تعرف على كيفية تدفق العمليات المختلفة في النظام من البداية إلى النهاية',

    // Workflow Types
    leaveRequest: 'طلب إجازة',
    expenseApproval: 'موافقة مصروف',
    projectCreation: 'إنشاء مشروع',
    purchaseOrder: 'أمر شراء',

    // Actions
    playDemo: 'تشغيل العرض',
    pauseDemo: 'إيقاف مؤقت',
    resetDemo: 'إعادة تشغيل',

    // Steps
    step: 'خطوة',
    pending: 'في الانتظار',
    approved: 'موافق عليه',
    rejected: 'مرفوض',
    completed: 'مكتمل',

    // Workflow Steps
    submitRequest: 'تقديم الطلب',
    managerReview: 'مراجعة المدير',
    hrApproval: 'موافقة الموارد البشرية',
    finalApproval: 'الموافقة النهائية',
    notification: 'إشعار النتيجة',

    // Demo Controls
    currentStep: 'الخطوة الحالية',
    nextStep: 'الخطوة التالية',
    workflow: 'سير العمل'
  },
  en: {
    title: 'Workflow Demo',
    subtitle: 'See how processes work in the system',
    description: 'Learn how different processes flow through the system from start to finish',

    // Workflow Types
    leaveRequest: 'Leave Request',
    expenseApproval: 'Expense Approval',
    projectCreation: 'Project Creation',
    purchaseOrder: 'Purchase Order',

    // Actions
    playDemo: 'Play Demo',
    pauseDemo: 'Pause Demo',
    resetDemo: 'Reset Demo',

    // Steps
    step: 'Step',
    pending: 'Pending',
    approved: 'Approved',
    rejected: 'Rejected',
    completed: 'Completed',

    // Workflow Steps
    submitRequest: 'Submit Request',
    managerReview: 'Manager Review',
    hrApproval: 'HR Approval',
    finalApproval: 'Final Approval',
    notification: 'Result Notification',

    // Demo Controls
    currentStep: 'Current Step',
    nextStep: 'Next Step',
    workflow: 'Workflow'
  }
}

export default function WorkflowDemo({ language }: WorkflowDemoProps): void {
  const [selectedWorkflow, setSelectedWorkflow] = useState('leaveRequest')
  const [currentStep, setCurrentStep] = useState(0)
  const [isPlaying, setIsPlaying] = useState(false)
  const [demoInterval, setDemoInterval] = useState<NodeJS.Timeout | null>(null)

  const t = translations[language]
  const isRTL = language === 'ar'

  const workflows = {
    leaveRequest: {
      title: t.leaveRequest,
      icon: Calendar,
      color: 'from-blue-500 to-cyan-500',
      steps: [
        {
          title: language === 'ar' ? 'الموظف يقدم طلب إجازة' : 'Employee submits leave request',
          description: language === 'ar' ? 'يملأ الموظف نموذج طلب الإجازة' : 'Employee fills out leave request form',
          icon: User,
          actor: language === 'ar' ? 'الموظف' : 'Employee',
          status: 'active'
        },
        {
          title: language === 'ar' ? 'إشعار المدير المباشر' : 'Direct manager notification',
          description: language === 'ar' ? 'يتم إرسال إشعار للمدير المباشر' : 'Notification sent to direct manager',
          icon: Send,
          actor: language === 'ar' ? 'النظام' : 'System',
          status: 'pending'
        },
        {
          title: language === 'ar' ? 'مراجعة المدير' : 'Manager review',
          description: language === 'ar' ? 'المدير يراجع ويوافق أو يرفض الطلب' : 'Manager reviews and approves or rejects request',
          icon: Eye,
          actor: language === 'ar' ? 'المدير' : 'Manager',
          status: 'pending'
        },
        {
          title: language === 'ar' ? 'موافقة الموارد البشرية' : 'HR approval',
          description: language === 'ar' ? 'قسم الموارد البشرية يراجع الطلب' : 'HR department reviews the request',
          icon: Users,
          actor: language === 'ar' ? 'الموارد البشرية' : 'HR',
          status: 'pending'
        },
        {
          title: language === 'ar' ? 'إشعار النتيجة' : 'Result notification',
          description: language === 'ar' ? 'إشعار الموظف بنتيجة الطلب' : 'Employee notified of request result',
          icon: CheckCircle,
          actor: language === 'ar' ? 'النظام' : 'System',
          status: 'pending'
        }
      ]
    },
    expenseApproval: {
      title: t.expenseApproval,
      icon: DollarSign,
      color: 'from-green-500 to-emerald-500',
      steps: [
        {
          title: language === 'ar' ? 'تقديم طلب مصروف' : 'Submit expense request',
          description: language === 'ar' ? 'الموظف يرفع إيصالات المصروفات' : 'Employee uploads expense receipts',
          icon: FileText,
          actor: language === 'ar' ? 'الموظف' : 'Employee',
          status: 'active'
        },
        {
          title: language === 'ar' ? 'مراجعة المدير' : 'Manager review',
          description: language === 'ar' ? 'المدير يراجع المصروفات والإيصالات' : 'Manager reviews expenses and receipts',
          icon: Eye,
          actor: language === 'ar' ? 'المدير' : 'Manager',
          status: 'pending'
        },
        {
          title: language === 'ar' ? 'مراجعة المالية' : 'Finance review',
          description: language === 'ar' ? 'قسم المالية يراجع ويوافق على الدفع' : 'Finance department reviews and approves payment',
          icon: DollarSign,
          actor: language === 'ar' ? 'المالية' : 'Finance',
          status: 'pending'
        },
        {
          title: language === 'ar' ? 'معالجة الدفع' : 'Process payment',
          description: language === 'ar' ? 'تتم معالجة الدفع للموظف' : 'Payment is processed to employee',
          icon: CheckCircle,
          actor: language === 'ar' ? 'النظام' : 'System',
          status: 'pending'
        }
      ]
    },
    projectCreation: {
      title: t.projectCreation,
      icon: Package,
      color: 'from-purple-500 to-pink-500',
      steps: [
        {
          title: language === 'ar' ? 'اقتراح المشروع' : 'Project proposal',
          description: language === 'ar' ? 'تقديم اقتراح مشروع جديد' : 'Submit new project proposal',
          icon: FileText,
          actor: language === 'ar' ? 'مدير المشروع' : 'Project Manager',
          status: 'active'
        },
        {
          title: language === 'ar' ? 'مراجعة الإدارة' : 'Management review',
          description: language === 'ar' ? 'الإدارة العليا تراجع المشروع' : 'Upper management reviews project',
          icon: Eye,
          actor: language === 'ar' ? 'الإدارة' : 'Management',
          status: 'pending'
        },
        {
          title: language === 'ar' ? 'تخصيص الموارد' : 'Resource allocation',
          description: language === 'ar' ? 'تخصيص الموارد والفريق للمشروع' : 'Allocate resources and team to project',
          icon: Users,
          actor: language === 'ar' ? 'الموارد البشرية' : 'HR',
          status: 'pending'
        },
        {
          title: language === 'ar' ? 'بدء المشروع' : 'Project kickoff',
          description: language === 'ar' ? 'بدء تنفيذ المشروع رسمياً' : 'Officially start project execution',
          icon: CheckCircle,
          actor: language === 'ar' ? 'مدير المشروع' : 'Project Manager',
          status: 'pending'
        }
      ]
    }
  }

  const playDemo = (): void => {
    if (isPlaying) {
      if (demoInterval) {
        clearInterval(demoInterval)
        setDemoInterval(null)
      }
      setIsPlaying(false)
    } else {
      setIsPlaying(true)
      const interval = setInterval(() => {
        setCurrentStep((prev) => {
          const maxSteps = workflows[selectedWorkflow as keyof typeof workflows].steps.length
          if (prev >= maxSteps - 1) {
            setIsPlaying(false)
            clearInterval(interval)
            return prev
          }
          return prev + 1
        })
      }, 2000)
      setDemoInterval(interval)
    }
  }

  const resetDemo = (): void => {
    if (demoInterval) {
      clearInterval(demoInterval)
      setDemoInterval(null)
    }
    setIsPlaying(false)
    setCurrentStep(0)
  }

  const selectedWorkflowData = workflows[selectedWorkflow as keyof typeof workflows]

  return (
    <div className={`space-y-10 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="text-center animate-fade-in-up">
        <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-white via-white to-white/80 bg-clip-text text-transparent mb-6">{t.title}</h2>
        <p className="text-xl text-white/90 mb-4">{t.subtitle}</p>
        <p className="text-white/70 max-w-3xl mx-auto text-lg">{t.description}</p>
      </div>

      {/* Workflow Selector */}
      <div className="flex flex-wrap justify-center gap-3 animate-fade-in-up animation-delay-200">
        {Object.entries(workflows).map(([key, workflow]) => {
          const Icon = workflow.icon
          return (
            <Button
              key={key}
              onClick={() => {
                setSelectedWorkflow(key)
                resetDemo()
              }}
              className={`glass-button transition-all duration-300 ${
                selectedWorkflow === key
                  ? 'bg-gradient-to-r from-blue-500 to-purple-500 border-0 text-white shadow-lg scale-105'
                  : 'border-white/20 hover:border-white/40 hover:bg-white/10'
              }`}
            >
              <Icon className="h-4 w-4 mr-2" />
              {workflow.title}
            </Button>
          )
        })}
      </div>

      {/* Demo Controls */}
      <div className="flex justify-center gap-4 animate-fade-in-up animation-delay-300">
        <Button onClick={playDemo} className="glass-button border-white/20 hover:border-white/40 hover:bg-white/10 transition-all duration-300">
          {isPlaying ? <Pause className="h-4 w-4 mr-2" /> : <Play className="h-4 w-4 mr-2" />}
          {isPlaying ? t.pauseDemo : t.playDemo}
        </Button>
        <Button onClick={resetDemo} className="glass-button border-white/20 hover:border-white/40 hover:bg-white/10 transition-all duration-300">
          <RotateCcw className="h-4 w-4 mr-2" />
          {t.resetDemo}
        </Button>
      </div>

      {/* Workflow Visualization */}
      <Card className="glass-card border-white/20 hover:border-white/30 transition-all duration-300 animate-fade-in-up animation-delay-400">
        <CardHeader>
          <CardTitle className="text-white text-center text-xl font-semibold">
            <span className="bg-gradient-to-r from-white to-white/80 bg-clip-text text-transparent">
              {selectedWorkflowData.title}
            </span>
            <span className="text-white/70 text-base ml-2">
              - {t.currentStep}: {currentStep + 1}
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {selectedWorkflowData.steps.map((step, index) => {
              const Icon = step.icon
              const isActive = index === currentStep
              const isCompleted = index < currentStep
              const isPending = index > currentStep

              return (
                <div key={index} className="flex items-center gap-4">
                  {/* Step Number */}
                  <div className={`w-12 h-12 rounded-full flex items-center justify-center border-2 transition-all duration-500 shadow-lg ${
                    isCompleted
                      ? 'bg-gradient-to-r from-green-500 to-emerald-500 border-green-400 shadow-green-500/30'
                      : isActive
                        ? 'bg-gradient-to-r from-blue-500 to-purple-500 border-blue-400 animate-pulse shadow-blue-500/30'
                        : 'border-white/30 bg-white/5'
                  }`}>
                    {isCompleted ? (
                      <CheckCircle className="h-6 w-6 text-white" />
                    ) : (
                      <span className="text-white font-bold text-lg">{index + 1}</span>
                    )}
                  </div>

                  {/* Step Content */}
                  <div className={`flex-1 p-5 rounded-xl border transition-all duration-500 ${
                    isActive
                      ? 'glass-card border-white/40 shadow-lg'
                      : isCompleted
                        ? 'bg-green-500/10 border-green-500/30 shadow-md'
                        : 'bg-white/5 border-white/20'
                  }`}>
                    <div className="flex items-center gap-3 mb-3">
                      <Icon className={`h-6 w-6 ${
                        isCompleted ? 'text-green-400' : isActive ? 'text-blue-400' : 'text-white/60'
                      }`} />
                      <h4 className={`font-semibold text-lg ${
                        isCompleted ? 'text-green-400' : isActive ? 'text-white' : 'text-white/60'
                      }`}>
                        {step.title}
                      </h4>
                      <span className={`text-xs px-3 py-1 rounded-full font-medium ${
                        isCompleted ? 'bg-green-500/20 text-green-400 border border-green-500/30' : isActive ? 'bg-blue-500/20 text-blue-400 border border-blue-500/30' : 'bg-white/10 text-white/60 border border-white/20'
                      }`}>
                        {step.actor}
                      </span>
                    </div>
                    <p className={`text-sm leading-relaxed ${
                      isCompleted ? 'text-green-300' : isActive ? 'text-white/90' : 'text-white/50'
                    }`}>
                      {step.description}
                    </p>
                  </div>

                  {/* Arrow */}
                  {index < selectedWorkflowData.steps.length - 1 && (
                    <ArrowRight className={`h-6 w-6 transition-colors duration-500 ${
                      isCompleted ? 'text-green-400' : 'text-white/30'
                    }`} />
                  )}
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
