/**
 * KPI Filter Panel Component
 * Advanced filtering panel for KPI data with multiple filter types
 */

import React, { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/ui/accordion'
import { Badge } from '@/components/ui/badge'
import { Slider } from '@/components/ui/slider'
import { DateRangePicker, DateRange } from '@/components/common/DateRangePicker'
import { KPICategory } from '@/services/kpiService'
import { Filter, X, Check, RefreshCw, Calendar, Target, TrendingUp, TrendingDown, Activity } from 'lucide-react'

export interface KPIFilterValue {
  categories?: string[]
  status?: string
  frequency?: string
  measurementType?: string
  trendDirection?: string
  targetAchievement?: [number, number] // Min and max percentage
  dateRange?: DateRange
  search?: string
}

export interface KPIFilterPanelProps {
  categories: KPICategory[]
  onFilterChange: (filters: KPIFilterValue) => void
  initialFilters?: KPIFilterValue
  language: 'ar' | 'en'
  onReset?: () => void
  loading?: boolean
}

const translations = {
  ar: {
    filters: 'الفلاتر',
    categories: 'الفئات',
    status: 'الحالة',
    frequency: 'التكرار',
    measurementType: 'نوع القياس',
    trendDirection: 'اتجاه الاتجاه',
    targetAchievement: 'تحقيق الهدف',
    dateRange: 'النطاق الزمني',
    search: 'بحث',
    active: 'نشط',
    inactive: 'غير نشط',
    archived: 'مؤرشف',
    daily: 'يومي',
    weekly: 'أسبوعي',
    monthly: 'شهري',
    quarterly: 'ربع سنوي',
    yearly: 'سنوي',
    number: 'رقم',
    percentage: 'نسبة مئوية',
    currency: 'عملة',
    up: 'صعود',
    down: 'هبوط',
    stable: 'مستقر',
    any: 'الكل',
    selectAll: 'اختر الكل',
    clearAll: 'مسح الكل',
    apply: 'تطبيق',
    reset: 'إعادة تعيين',
    activeFilters: 'الفلاتر النشطة',
    noFilters: 'لا توجد فلاتر نشطة',
    min: 'الحد الأدنى',
    max: 'الحد الأقصى'
  },
  en: {
    filters: 'Filters',
    categories: 'Categories',
    status: 'Status',
    frequency: 'Frequency',
    measurementType: 'Measurement Type',
    trendDirection: 'Trend Direction',
    targetAchievement: 'Target Achievement',
    dateRange: 'Date Range',
    search: 'Search',
    active: 'Active',
    inactive: 'Inactive',
    archived: 'Archived',
    daily: 'Daily',
    weekly: 'Weekly',
    monthly: 'Monthly',
    quarterly: 'Quarterly',
    yearly: 'Yearly',
    number: 'Number',
    percentage: 'Percentage',
    currency: 'Currency',
    up: 'Up',
    down: 'Down',
    stable: 'Stable',
    any: 'Any',
    selectAll: 'Select All',
    clearAll: 'Clear All',
    apply: 'Apply',
    reset: 'Reset',
    activeFilters: 'Active Filters',
    noFilters: 'No active filters',
    min: 'Min',
    max: 'Max'
  }
}

export function KPIFilterPanel({
  categories,
  onFilterChange,
  initialFilters = {},
  language,
  onReset,
  loading = false
}: KPIFilterPanelProps): void {
  const [filters, setFilters] = useState<KPIFilterValue>(initialFilters)
  const [selectedCategories, setSelectedCategories] = useState<string[]>(initialFilters.categories || [])
  const [targetRange, setTargetRange] = useState<[number, number]>(initialFilters.targetAchievement || [0, 200])
  const [activeFilterCount, setActiveFilterCount] = useState<number>(0)
  
  const t = translations[language]
  const isRTL = language === 'ar'

  // Initialize filters from props
  useEffect(() => {
    setFilters(initialFilters)
    setSelectedCategories(initialFilters.categories || [])
    setTargetRange(initialFilters.targetAchievement || [0, 200])
  }, [initialFilters])

  // Count active filters
  useEffect(() => {
    let count = 0
    if (filters.categories?.length) count++
    if (filters.status) count++
    if (filters.frequency) count++
    if (filters.measurementType) count++
    if (filters.trendDirection) count++
    if (filters.targetAchievement && 
        (filters.targetAchievement[0] > 0 || filters.targetAchievement[1] < 200)) count++
    if (filters.dateRange) count++
    if (filters.search) count++
    setActiveFilterCount(count)
  }, [filters])

  // Handle category selection
  const handleCategoryChange = (categoryId: string, checked: boolean): void => {
    let newCategories: string[]
    
    if (checked) {
      newCategories = [...selectedCategories, categoryId]
    } else {
      newCategories = selectedCategories.filter(id => id !== categoryId)
    }
    
    setSelectedCategories(newCategories)
    
    const newFilters = {
      ...filters,
      categories: newCategories.length > 0 ? newCategories : undefined
    }
    
    setFilters(newFilters)
    onFilterChange(newFilters)
  }

  // Handle select all categories
  const handleSelectAllCategories = (): void => {
    const allCategoryIds = categories.map(cat => cat.id)
    setSelectedCategories(allCategoryIds)
    
    const newFilters = {
      ...filters,
      categories: allCategoryIds
    }
    
    setFilters(newFilters)
    onFilterChange(newFilters)
  }

  // Handle clear all categories
  const handleClearAllCategories = (): void => {
    setSelectedCategories([])
    
    const newFilters = {
      ...filters,
      categories: undefined
    }
    
    setFilters(newFilters)
    onFilterChange(newFilters)
  }

  // Handle select change
  const handleSelectChange = (key: keyof KPIFilterValue, value: string): void => {
    const newFilters = {
      ...filters,
      [key]: value === 'any' ? undefined : value
    }
    
    setFilters(newFilters)
    onFilterChange(newFilters)
  }

  // Handle target achievement range change
  const handleTargetRangeChange = (value: [number, number]): void => {
    setTargetRange(value)
    
    const newFilters = {
      ...filters,
      targetAchievement: value[0] === 0 && value[1] === 200 ? undefined : value
    }
    
    setFilters(newFilters)
    onFilterChange(newFilters)
  }

  // Handle date range change
  const handleDateRangeChange = (range: DateRange): void => {
    const newFilters = {
      ...filters,
      dateRange: range
    }
    
    setFilters(newFilters)
    onFilterChange(newFilters)
  }

  // Handle search change
  const handleSearchChange = (value: string): void => {
    const newFilters = {
      ...filters,
      search: value || undefined
    }
    
    setFilters(newFilters)
    onFilterChange(newFilters)
  }

  // Handle reset
  const handleReset = (): void => {
    setFilters({})
    setSelectedCategories([])
    setTargetRange([0, 200])
    onFilterChange({})
    if (onReset) onReset()
  }

  return (
    <div className={`glass-card border-white/20 p-4 rounded-lg space-y-4 ${isRTL ? 'rtl' : 'ltr'}`}>
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-white flex items-center gap-2">
          <Filter className="h-5 w-5" />
          {t.filters}
          {activeFilterCount > 0 && (
            <Badge className="bg-blue-500/30 text-blue-200 border-blue-500/50">
              {activeFilterCount}
            </Badge>
          )}
        </h3>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleReset}
          disabled={loading}
          className="text-white/70 hover:text-white hover:bg-white/10"
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          {t.reset}
        </Button>
      </div>

      <div className="space-y-4">
        {/* Search */}
        <div className="space-y-2">
          <Label htmlFor="search" className="text-white">{t.search}</Label>
          <Input
            id="search"
            value={filters.search || ''}
            onChange={(e) => handleSearchChange(e.target.value)}
            placeholder={language === 'ar' ? 'البحث في المؤشرات...' : 'Search KPIs...'}
            className="glass-input"
          />
        </div>

        {/* Date Range */}
        <div className="space-y-2">
          <Label className="text-white">{t.dateRange}</Label>
          <DateRangePicker
            value={filters.dateRange}
            onChange={handleDateRangeChange}
            locale={language}
            className="w-full"
          />
        </div>

        <Accordion type="multiple" defaultValue={['categories', 'status']} className="space-y-2">
          {/* Categories */}
          <AccordionItem value="categories" className="border-white/10">
            <AccordionTrigger className="text-white hover:no-underline py-2">
              {t.categories}
            </AccordionTrigger>
            <AccordionContent>
              <div className="space-y-2">
                <div className="flex justify-between mb-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleSelectAllCategories}
                    className="text-xs text-white/70 hover:text-white hover:bg-white/10"
                  >
                    {t.selectAll}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleClearAllCategories}
                    className="text-xs text-white/70 hover:text-white hover:bg-white/10"
                  >
                    {t.clearAll}
                  </Button>
                </div>
                <div className="space-y-2 max-h-40 overflow-y-auto pr-2">
                  {categories.map((category) => (
                    <div key={category.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={`category-${category.id}`}
                        checked={selectedCategories.includes(category.id)}
                        onCheckedChange={(checked) => handleCategoryChange(category.id, !!checked)}
                      />
                      <Label
                        htmlFor={`category-${category.id}`}
                        className="text-white cursor-pointer flex items-center"
                      >
                        <div
                          className="w-3 h-3 rounded-full mr-2"
                          style={{ backgroundColor: category.color }}
                        />
                        {language === 'ar' ? category.name_ar : category.name}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>

          {/* Status */}
          <AccordionItem value="status" className="border-white/10">
            <AccordionTrigger className="text-white hover:no-underline py-2">
              {t.status}
            </AccordionTrigger>
            <AccordionContent>
              <Select
                value={filters.status || 'any'}
                onValueChange={(value) => handleSelectChange('status', value)}
              >
                <SelectTrigger className="glass-input">
                  <SelectValue placeholder={t.any} />
                </SelectTrigger>
                <SelectContent className="glass-card border-white/20">
                  <SelectItem value="any">{t.any}</SelectItem>
                  <SelectItem value="active">{t.active}</SelectItem>
                  <SelectItem value="inactive">{t.inactive}</SelectItem>
                  <SelectItem value="archived">{t.archived}</SelectItem>
                </SelectContent>
              </Select>
            </AccordionContent>
          </AccordionItem>

          {/* Frequency */}
          <AccordionItem value="frequency" className="border-white/10">
            <AccordionTrigger className="text-white hover:no-underline py-2">
              {t.frequency}
            </AccordionTrigger>
            <AccordionContent>
              <Select
                value={filters.frequency || 'any'}
                onValueChange={(value) => handleSelectChange('frequency', value)}
              >
                <SelectTrigger className="glass-input">
                  <SelectValue placeholder={t.any} />
                </SelectTrigger>
                <SelectContent className="glass-card border-white/20">
                  <SelectItem value="any">{t.any}</SelectItem>
                  <SelectItem value="daily">{t.daily}</SelectItem>
                  <SelectItem value="weekly">{t.weekly}</SelectItem>
                  <SelectItem value="monthly">{t.monthly}</SelectItem>
                  <SelectItem value="quarterly">{t.quarterly}</SelectItem>
                  <SelectItem value="yearly">{t.yearly}</SelectItem>
                </SelectContent>
              </Select>
            </AccordionContent>
          </AccordionItem>

          {/* Measurement Type */}
          <AccordionItem value="measurementType" className="border-white/10">
            <AccordionTrigger className="text-white hover:no-underline py-2">
              {t.measurementType}
            </AccordionTrigger>
            <AccordionContent>
              <Select
                value={filters.measurementType || 'any'}
                onValueChange={(value) => handleSelectChange('measurementType', value)}
              >
                <SelectTrigger className="glass-input">
                  <SelectValue placeholder={t.any} />
                </SelectTrigger>
                <SelectContent className="glass-card border-white/20">
                  <SelectItem value="any">{t.any}</SelectItem>
                  <SelectItem value="number">{t.number}</SelectItem>
                  <SelectItem value="percentage">{t.percentage}</SelectItem>
                  <SelectItem value="currency">{t.currency}</SelectItem>
                </SelectContent>
              </Select>
            </AccordionContent>
          </AccordionItem>

          {/* Trend Direction */}
          <AccordionItem value="trendDirection" className="border-white/10">
            <AccordionTrigger className="text-white hover:no-underline py-2">
              {t.trendDirection}
            </AccordionTrigger>
            <AccordionContent>
              <Select
                value={filters.trendDirection || 'any'}
                onValueChange={(value) => handleSelectChange('trendDirection', value)}
              >
                <SelectTrigger className="glass-input">
                  <SelectValue placeholder={t.any} />
                </SelectTrigger>
                <SelectContent className="glass-card border-white/20">
                  <SelectItem value="any">{t.any}</SelectItem>
                  <SelectItem value="up" className="flex items-center">
                    <TrendingUp className="h-4 w-4 mr-2 text-green-400" />
                    {t.up}
                  </SelectItem>
                  <SelectItem value="down" className="flex items-center">
                    <TrendingDown className="h-4 w-4 mr-2 text-red-400" />
                    {t.down}
                  </SelectItem>
                  <SelectItem value="stable" className="flex items-center">
                    <Activity className="h-4 w-4 mr-2 text-blue-400" />
                    {t.stable}
                  </SelectItem>
                </SelectContent>
              </Select>
            </AccordionContent>
          </AccordionItem>

          {/* Target Achievement */}
          <AccordionItem value="targetAchievement" className="border-white/10">
            <AccordionTrigger className="text-white hover:no-underline py-2">
              {t.targetAchievement}
            </AccordionTrigger>
            <AccordionContent>
              <div className="space-y-4">
                <Slider
                  value={targetRange}
                  min={0}
                  max={200}
                  step={5}
                  onValueChange={handleTargetRangeChange}
                  className="my-6"
                />
                <div className="flex justify-between text-sm text-white/70">
                  <div>
                    {t.min}: {targetRange[0]}%
                  </div>
                  <div>
                    {t.max}: {targetRange[1]}%
                  </div>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </div>
    </div>
  )
}

export default KPIFilterPanel
