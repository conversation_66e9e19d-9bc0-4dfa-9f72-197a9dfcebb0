import React from 'react';
import { useEffect, useState, useCallback } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Zap,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Monitor,
  Smartphone,
  Wifi,
  Battery,
  Clock,
  MemoryStick,
  HardDrive,
  Cpu
} from 'lucide-react'
import usePerformanceMonitoring from '../../hooks/usePerformanceMonitoring'

interface PerformanceOptimizerProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    title: 'محسن الأداء',
    subtitle: 'مراقبة وتحسين أداء النظام في الوقت الفعلي',
    overallScore: 'النقاط الإجمالية',
    excellent: 'ممتاز',
    good: 'جيد',
    needsImprovement: 'يحتاج تحسين',
    poor: 'ضعيف',
    metrics: 'المقاييس',
    loadTime: 'وقت التحميل',
    memoryUsage: 'استخدام الذاكرة',
    networkSpeed: 'سرعة الشبكة',
    deviceType: 'نوع الجهاز',
    optimizations: 'التحسينات',
    enableOptimizations: 'تفعيل التحسينات',
    clearCache: 'مسح التخزين المؤقت',
    preloadPages: 'تحميل الصفحات مسبقاً',
    optimizeImages: 'تحسين الصور',
    enableCompression: 'تفعيل الضغط',
    recommendations: 'التوصيات',
    alerts: 'التنبيهات',
    noAlerts: 'لا توجد تنبيهات',
    refreshMetrics: 'تحديث المقاييس',
    exportReport: 'تصدير التقرير',
    webVitals: 'مقاييس الويب الأساسية',
    fcp: 'أول رسم للمحتوى',
    lcp: 'أكبر رسم للمحتوى',
    cls: 'تحول التخطيط التراكمي',
    fid: 'تأخير الإدخال الأول',
    online: 'متصل',
    offline: 'غير متصل',
    mobile: 'جوال',
    tablet: 'لوحي',
    desktop: 'سطح المكتب',
    fast: 'سريع',
    medium: 'متوسط',
    slow: 'بطيء'
  },
  en: {
    title: 'Performance Optimizer',
    subtitle: 'Monitor and optimize system performance in real-time',
    overallScore: 'Overall Score',
    excellent: 'Excellent',
    good: 'Good',
    needsImprovement: 'Needs Improvement',
    poor: 'Poor',
    metrics: 'Metrics',
    loadTime: 'Load Time',
    memoryUsage: 'Memory Usage',
    networkSpeed: 'Network Speed',
    deviceType: 'Device Type',
    optimizations: 'Optimizations',
    enableOptimizations: 'Enable Optimizations',
    clearCache: 'Clear Cache',
    preloadPages: 'Preload Pages',
    optimizeImages: 'Optimize Images',
    enableCompression: 'Enable Compression',
    recommendations: 'Recommendations',
    alerts: 'Alerts',
    noAlerts: 'No alerts',
    refreshMetrics: 'Refresh Metrics',
    exportReport: 'Export Report',
    webVitals: 'Web Vitals',
    fcp: 'First Contentful Paint',
    lcp: 'Largest Contentful Paint',
    cls: 'Cumulative Layout Shift',
    fid: 'First Input Delay',
    online: 'Online',
    offline: 'Offline',
    mobile: 'Mobile',
    tablet: 'Tablet',
    desktop: 'Desktop',
    fast: 'Fast',
    medium: 'Medium',
    slow: 'Slow'
  }
}

export default function PerformanceOptimizer({ language }: PerformanceOptimizerProps): void {
  const [optimizationsEnabled, setOptimizationsEnabled] = useState(false)
  const [isOptimizing, setIsOptimizing] = useState(false)
  const [lastOptimized, setLastOptimized] = useState<Date | null>(null)
  
  const {
    metrics,
    alerts,
    isMonitoring,
    startMonitoring,
    stopMonitoring,
    clearAlerts,
    getPerformanceScore
  } = usePerformanceMonitoring()

  const t = translations[language]
  const isRTL = language === 'ar'
  const performanceScore = getPerformanceScore()

  // Start monitoring on component mount
  useEffect(() => {
    if (!isMonitoring) {
      startMonitoring()
    }
    return () => stopMonitoring()
  }, [isMonitoring, startMonitoring, stopMonitoring])

  // Get performance level and color
  const getPerformanceLevel = (score: number): void => {
    if (score >= 90) return { level: t.excellent, color: 'text-green-400', bgColor: 'bg-green-500/20' }
    if (score >= 75) return { level: t.good, color: 'text-blue-400', bgColor: 'bg-blue-500/20' }
    if (score >= 50) return { level: t.needsImprovement, color: 'text-yellow-400', bgColor: 'bg-yellow-500/20' }
    return { level: t.poor, color: 'text-red-400', bgColor: 'bg-red-500/20' }
  }

  // Format time in milliseconds
  const formatTime = (ms: number): string => {
    if (ms < 1000) return `${Math.round(ms)}ms`
    return `${(ms / 1000).toFixed(1)}s`
  }

  // Format bytes
  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`
  }

  // Clear cache
  const clearCache = useCallback(async () => {
    try {
      if ('caches' in window) {
        const cacheNames = await caches.keys()
        await Promise.all(cacheNames.map(name => caches.delete(name)))
      }
      localStorage.clear()
      sessionStorage.clear()

      // FIXED: Use refresh event instead of page reload
      window.dispatchEvent(new CustomEvent('app:force-refresh', {
        detail: { timestamp: Date.now(), source: 'cache-clear' }
      }))

      // Update metrics to reflect cache clear
      setMetrics(getPerformanceMetrics())
      setAlerts(getPerformanceAlerts())
    } catch (error) {
      console.error('Failed to clear cache:', error)
    }
  }, [])

  // Preload critical pages
  const preloadPages = useCallback(async () => {
    const criticalPages = [
      '/admin/dashboard',
      '/admin/employees',
      '/admin/projects',
      '/admin/reports'
    ]

    try {
      await Promise.all(
        criticalPages.map(page => 
          fetch(page, { method: 'HEAD' }).catch(() => {})
        )
      )
    } catch (error) {
      console.error('Failed to preload pages:', error)
    }
  }, [])

  // Enable optimizations
  const enableOptimizations = useCallback(async () => {
    setIsOptimizing(true)
    
    try {
      // Enable various optimizations
      await preloadPages()
      
      // Enable service worker if available
      if ('serviceWorker' in navigator) {
        await navigator.serviceWorker.register('/sw.js')
      }

      // Enable resource hints
      const link = document.createElement('link')
      link.rel = 'dns-prefetch'
      link.href = '//fonts.googleapis.com'
      document.head.appendChild(link)

      setOptimizationsEnabled(true)
      setLastOptimized(new Date())
    } catch (error) {
      console.error('Failed to enable optimizations:', error)
    } finally {
      setIsOptimizing(false)
    }
  }, [preloadPages])

  // Export performance report
  const exportReport = useCallback(() => {
    const report = {
      timestamp: new Date().toISOString(),
      performanceScore,
      metrics,
      alerts,
      recommendations: getRecommendations()
    }

    const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `performance-report-${Date.now()}.json`
    a.click()
    URL.revokeObjectURL(url)
  }, [performanceScore, metrics, alerts])

  // Get recommendations based on metrics
  const getRecommendations = useCallback(() => {
    const recommendations = []

    if (metrics.loadTime > 3000) {
      recommendations.push(language === 'ar' ? 'تحسين وقت التحميل' : 'Optimize load time')
    }
    if (metrics.memoryUsage.percentage > 80) {
      recommendations.push(language === 'ar' ? 'تقليل استخدام الذاكرة' : 'Reduce memory usage')
    }
    if (metrics.networkSpeed === 'slow') {
      recommendations.push(language === 'ar' ? 'تحسين للشبكات البطيئة' : 'Optimize for slow networks')
    }
    if (metrics.errorCount > 0) {
      recommendations.push(language === 'ar' ? 'إصلاح الأخطاء' : 'Fix JavaScript errors')
    }

    return recommendations
  }, [metrics, language])

  const performanceLevel = getPerformanceLevel(performanceScore)
  const recommendations = getRecommendations()

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-white mb-2">{t.title}</h1>
        <p className="text-white/80">{t.subtitle}</p>
      </div>

      {/* Overall Performance Score */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center"
      >
        <Card className="glass-card">
          <CardContent className="p-8">
            <div className="flex items-center justify-center mb-4">
              <div className={`w-24 h-24 rounded-full ${performanceLevel.bgColor} flex items-center justify-center`}>
                <span className={`text-3xl font-bold ${performanceLevel.color}`}>
                  {performanceScore}
                </span>
              </div>
            </div>
            <h2 className="text-xl font-semibold text-white mb-2">{t.overallScore}</h2>
            <Badge className={`${performanceLevel.color} bg-white/10`}>
              {performanceLevel.level}
            </Badge>
          </CardContent>
        </Card>
      </motion.div>

      {/* Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Load Time */}
        <Card className="glass-card">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Clock className="h-8 w-8 text-blue-400" />
              <div>
                <p className="text-white/70 text-sm">{t.loadTime}</p>
                <p className="text-white font-semibold">{formatTime(metrics.loadTime)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Memory Usage */}
        <Card className="glass-card">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <MemoryStick className="h-8 w-8 text-green-400" />
              <div>
                <p className="text-white/70 text-sm">{t.memoryUsage}</p>
                <p className="text-white font-semibold">
                  {metrics.memoryUsage.percentage.toFixed(1)}%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Network Speed */}
        <Card className="glass-card">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Wifi className="h-8 w-8 text-yellow-400" />
              <div>
                <p className="text-white/70 text-sm">{t.networkSpeed}</p>
                <p className="text-white font-semibold">{t[metrics.networkSpeed]}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Device Type */}
        <Card className="glass-card">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              {metrics.deviceType === 'mobile' && <Smartphone className="h-8 w-8 text-purple-400" />}
              {metrics.deviceType === 'tablet' && <Monitor className="h-8 w-8 text-purple-400" />}
              {metrics.deviceType === 'desktop' && <Monitor className="h-8 w-8 text-purple-400" />}
              <div>
                <p className="text-white/70 text-sm">{t.deviceType}</p>
                <p className="text-white font-semibold">{t[metrics.deviceType]}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Web Vitals */}
      <Card className="glass-card">
        <CardHeader>
          <CardTitle className="text-white">{t.webVitals}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="text-center">
              <p className="text-white/70 text-sm">{t.fcp}</p>
              <p className="text-white font-semibold">{formatTime(metrics.firstContentfulPaint)}</p>
            </div>
            <div className="text-center">
              <p className="text-white/70 text-sm">{t.lcp}</p>
              <p className="text-white font-semibold">{formatTime(metrics.largestContentfulPaint)}</p>
            </div>
            <div className="text-center">
              <p className="text-white/70 text-sm">{t.cls}</p>
              <p className="text-white font-semibold">{metrics.cumulativeLayoutShift.toFixed(3)}</p>
            </div>
            <div className="text-center">
              <p className="text-white/70 text-sm">{t.fid}</p>
              <p className="text-white font-semibold">{formatTime(metrics.firstInputDelay)}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Optimizations */}
      <Card className="glass-card">
        <CardHeader>
          <CardTitle className="text-white">{t.optimizations}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-white">{t.enableOptimizations}</span>
              <Button
                onClick={enableOptimizations}
                disabled={isOptimizing || optimizationsEnabled}
                className="glass-button"
              >
                {isOptimizing ? (
                  <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                ) : optimizationsEnabled ? (
                  <CheckCircle className="h-4 w-4 mr-2" />
                ) : (
                  <Zap className="h-4 w-4 mr-2" />
                )}
                {optimizationsEnabled ? 'مُفعل' : t.enableOptimizations}
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Button onClick={clearCache} variant="outline" className="glass-button">
                <HardDrive className="h-4 w-4 mr-2" />
                {t.clearCache}
              </Button>
              <Button onClick={preloadPages} variant="outline" className="glass-button">
                <TrendingUp className="h-4 w-4 mr-2" />
                {t.preloadPages}
              </Button>
            </div>

            {lastOptimized && (
              <p className="text-white/60 text-sm">
                آخر تحسين: {lastOptimized.toLocaleString(language === 'ar' ? 'ar-SA' : 'en-US')}
              </p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Recommendations */}
      {recommendations.length > 0 && (
        <Card className="glass-card">
          <CardHeader>
            <CardTitle className="text-white">{t.recommendations}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {recommendations.map((recommendation, index) => (
                <div key={index} className="flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4 text-yellow-400" />
                  <span className="text-white/80">{recommendation}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Alerts */}
      <Card className="glass-card">
        <CardHeader>
          <CardTitle className="text-white flex items-center justify-between">
            {t.alerts}
            {alerts.length > 0 && (
              <Button onClick={clearAlerts} variant="ghost" size="sm" className="text-white/60">
                مسح الكل
              </Button>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {alerts.length === 0 ? (
            <div className="text-center py-8">
              <CheckCircle className="h-12 w-12 text-green-400 mx-auto mb-2" />
              <p className="text-white/60">{t.noAlerts}</p>
            </div>
          ) : (
            <div className="space-y-3">
              {alerts.slice(0, 5).map((alert, index) => (
                <div
                  key={index}
                  className={`p-3 rounded-lg border ${
                    alert.type === 'error' 
                      ? 'border-red-500/30 bg-red-500/10' 
                      : 'border-yellow-500/30 bg-yellow-500/10'
                  }`}
                >
                  <div className="flex items-center gap-2">
                    <AlertTriangle className={`h-4 w-4 ${
                      alert.type === 'error' ? 'text-red-400' : 'text-yellow-400'
                    }`} />
                    <span className="text-white text-sm">{alert.message}</span>
                  </div>
                  <p className="text-white/60 text-xs mt-1">
                    {alert.timestamp.toLocaleString(language === 'ar' ? 'ar-SA' : 'en-US')}
                  </p>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Actions */}
      <div className="flex gap-4 justify-center">
        <Button
          onClick={() => {
            // FIXED: Refresh metrics without page reload
            setMetrics(getPerformanceMetrics())
            setAlerts(getPerformanceAlerts())
          }}
          className="glass-button"
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          {t.refreshMetrics}
        </Button>
        <Button onClick={exportReport} variant="outline" className="glass-button">
          <TrendingUp className="h-4 w-4 mr-2" />
          {t.exportReport}
        </Button>
      </div>
    </div>
  )
}
