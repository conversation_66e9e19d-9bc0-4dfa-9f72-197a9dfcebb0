import React from 'react';
import { useState, useEffect, useCallback, useMemo } from 'react'
import { useNavigate } from 'react-router-dom'
import { useSelector } from 'react-redux'
import Fuse from 'fuse.js'
import {
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "@/components/ui/command"
import { Button } from "@/components/ui/button"
import {
  Search,
  Users,
  Building,
  Briefcase,
  DollarSign,
  FileText,
  Calendar,
  Settings,
  BarChart3,
  Package,
  MessageSquare,
  Clock,
  Target,
  Award,
  TrendingUp,
  Zap,
  Command
} from 'lucide-react'
import type { RootState } from '../../store'

interface cnProps {
  // TODO: Define proper prop types
  [key: string]: any;
}


interface SearchResult {
  id: string
  title: string
  titleAr: string
  description: string
  descriptionAr: string
  category: string
  categoryAr: string
  url: string
  icon: any
  keywords: string[]
  priority: number
}

interface AdvancedGlobalSearchProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    searchPlaceholder: 'ابحث في النظام... (Ctrl+K)',
    noResults: 'لا توجد نتائج',
    recentSearches: 'عمليات البحث الأخيرة',
    quickActions: 'إجراءات سريعة',
    pages: 'الصفحات',
    employees: 'الموظفون',
    departments: 'الأقسام',
    projects: 'المشاريع',
    reports: 'التقارير',
    settings: 'الإعدادات',
    searchIn: 'البحث في',
    allModules: 'جميع الوحدات',
    pressEnter: 'اضغط Enter للبحث',
    pressEscape: 'اضغط Esc للإغلاق'
  },
  en: {
    searchPlaceholder: 'Search system... (Ctrl+K)',
    noResults: 'No results found',
    recentSearches: 'Recent Searches',
    quickActions: 'Quick Actions',
    pages: 'Pages',
    employees: 'Employees',
    departments: 'Departments',
    projects: 'Projects',
    reports: 'Reports',
    settings: 'Settings',
    searchIn: 'Search in',
    allModules: 'All Modules',
    pressEnter: 'Press Enter to search',
    pressEscape: 'Press Esc to close'
  }
}

export default function AdvancedGlobalSearch({ language }: AdvancedGlobalSearchProps): void {
  const [open, setOpen] = useState(false)
  const [query, setQuery] = useState('')
  const [results, setResults] = useState<SearchResult[]>([])
  const [recentSearches, setRecentSearches] = useState<string[]>([])
  const navigate = useNavigate()
  const { user } = useSelector((state: RootState) => state.auth)
  const t = translations[language]
  const isRTL = language === 'ar'

  // Comprehensive search data based on user role
  const getSearchData = useCallback((): SearchResult[] => {
    const baseData: SearchResult[] = [
      // Dashboard & Analytics
      {
        id: 'dashboard',
        title: 'Dashboard',
        titleAr: 'لوحة التحكم',
        description: 'Main dashboard with overview',
        descriptionAr: 'لوحة التحكم الرئيسية مع نظرة عامة',
        category: 'Navigation',
        categoryAr: 'التنقل',
        url: '/',
        icon: BarChart3,
        keywords: ['dashboard', 'overview', 'main', 'لوحة', 'تحكم', 'رئيسية'],
        priority: 10
      },
      {
        id: 'analytics',
        title: 'Advanced Analytics',
        titleAr: 'التحليلات المتقدمة',
        description: 'Business intelligence and reports',
        descriptionAr: 'ذكاء الأعمال والتقارير',
        category: 'Analytics',
        categoryAr: 'التحليلات',
        url: '/analytics/advanced',
        icon: TrendingUp,
        keywords: ['analytics', 'reports', 'intelligence', 'تحليلات', 'تقارير', 'ذكاء'],
        priority: 8
      },

      // HR Management
      {
        id: 'employees',
        title: 'Employee Management',
        titleAr: 'إدارة الموظفين',
        description: 'Manage employees and HR',
        descriptionAr: 'إدارة الموظفين والموارد البشرية',
        category: 'HR',
        categoryAr: 'الموارد البشرية',
        url: '/employees',
        icon: Users,
        keywords: ['employees', 'hr', 'staff', 'موظفين', 'موارد', 'بشرية'],
        priority: 9
      },
      {
        id: 'attendance',
        title: 'Attendance Management',
        titleAr: 'إدارة الحضور',
        description: 'Track employee attendance',
        descriptionAr: 'تتبع حضور الموظفين',
        category: 'HR',
        categoryAr: 'الموارد البشرية',
        url: '/hr/attendance',
        icon: Clock,
        keywords: ['attendance', 'time', 'tracking', 'حضور', 'وقت', 'تتبع'],
        priority: 7
      },

      // Project Management
      {
        id: 'projects',
        title: 'Project Management',
        titleAr: 'إدارة المشاريع',
        description: 'Manage projects and tasks',
        descriptionAr: 'إدارة المشاريع والمهام',
        category: 'Projects',
        categoryAr: 'المشاريع',
        url: '/projects',
        icon: Briefcase,
        keywords: ['projects', 'tasks', 'management', 'مشاريع', 'مهام', 'إدارة'],
        priority: 8
      },
      {
        id: 'tasks',
        title: 'Task Management',
        titleAr: 'إدارة المهام',
        description: 'Manage and track tasks',
        descriptionAr: 'إدارة وتتبع المهام',
        category: 'Projects',
        categoryAr: 'المشاريع',
        url: '/projects/tasks',
        icon: Target,
        keywords: ['tasks', 'todo', 'assignments', 'مهام', 'واجبات', 'تكليفات'],
        priority: 7
      },

      // Financial Management
      {
        id: 'finance',
        title: 'Financial Management',
        titleAr: 'الإدارة المالية',
        description: 'Manage budgets and expenses',
        descriptionAr: 'إدارة الميزانيات والمصروفات',
        category: 'Finance',
        categoryAr: 'المالية',
        url: '/finance',
        icon: DollarSign,
        keywords: ['finance', 'budget', 'expenses', 'مالية', 'ميزانية', 'مصروفات'],
        priority: 8
      },

      // Communication
      {
        id: 'messages',
        title: 'Messages',
        titleAr: 'الرسائل',
        description: 'Internal messaging system',
        descriptionAr: 'نظام الرسائل الداخلي',
        category: 'Communication',
        categoryAr: 'التواصل',
        url: '/communication/messages',
        icon: MessageSquare,
        keywords: ['messages', 'chat', 'communication', 'رسائل', 'محادثة', 'تواصل'],
        priority: 6
      },

      // Settings
      {
        id: 'settings',
        title: 'System Settings',
        titleAr: 'إعدادات النظام',
        description: 'Configure system settings',
        descriptionAr: 'تكوين إعدادات النظام',
        category: 'Settings',
        categoryAr: 'الإعدادات',
        url: '/settings',
        icon: Settings,
        keywords: ['settings', 'configuration', 'preferences', 'إعدادات', 'تكوين', 'تفضيلات'],
        priority: 5
      }
    ]

    // Filter based on user role
    return baseData.filter(item => {
      if (!user?.role?.id) return true

      // Super admin sees everything
      if (user.role.id === 'super_admin') return true

      // Role-specific filtering
      switch (user.role.id) {
        case 'hr_manager':
          return ['dashboard', 'employees', 'attendance', 'settings'].includes(item.id)
        case 'finance_manager':
          return ['dashboard', 'finance', 'analytics', 'settings'].includes(item.id)
        case 'department_manager':
          return ['dashboard', 'projects', 'tasks', 'employees', 'messages'].includes(item.id)
        case 'sales_manager':
          return ['dashboard', 'projects', 'analytics', 'messages'].includes(item.id)
        case 'employee':
          return ['dashboard', 'tasks', 'messages', 'attendance'].includes(item.id)
        default:
          return true
      }
    })
  }, [user])

  // Initialize Fuse.js for fuzzy search - memoized to prevent infinite re-renders
  const fuse = useMemo(() => new Fuse(getSearchData(), {
    keys: [
      { name: 'title', weight: 0.3 },
      { name: 'titleAr', weight: 0.3 },
      { name: 'description', weight: 0.2 },
      { name: 'descriptionAr', weight: 0.2 },
      { name: 'keywords', weight: 0.4 },
      { name: 'category', weight: 0.1 },
      { name: 'categoryAr', weight: 0.1 }
    ],
    threshold: 0.3,
    includeScore: true,
    minMatchCharLength: 2
  }), [getSearchData])

  // Keyboard shortcut handler
  useEffect(() => {
    const down = (e: KeyboardEvent): void => {
      if (e.key === 'k' && (e.metaKey || e.ctrlKey)) {
        e.preventDefault()
        setOpen((open) => !open)
      }
    }

    document.addEventListener('keydown', down)
    return () => document.removeEventListener('keydown', down)
  }, [])

  // Search handler
  useEffect(() => {
    if (!query.trim()) {
      setResults([])
      return
    }

    const searchResults = fuse.search(query)
    const sortedResults = searchResults
      .map(result => result.item)
      .sort((a, b) => b.priority - a.priority)
      .slice(0, 10)

    setResults(sortedResults)
  }, [query, fuse])

  // Handle search selection
  const handleSelect = (result: SearchResult): void => {
    navigate(result.url)
    setOpen(false)

    // Add to recent searches
    const newRecentSearches = [query, ...recentSearches.filter(s => s !== query)].slice(0, 5)
    setRecentSearches(newRecentSearches)
    localStorage.setItem('recentSearches', JSON.stringify(newRecentSearches))

    setQuery('')
  }

  // Load recent searches
  useEffect(() => {
    const saved = localStorage.getItem('recentSearches')
    if (saved) {
      setRecentSearches(JSON.parse(saved))
    }
  }, [])

  return (
    <>
      {/* Search Trigger Button */}
      <Button
        variant="outline"
        className={cn(
          "relative h-9 w-9 p-0 xl:h-10 xl:w-60 xl:justify-start xl:px-3 xl:py-2",
          "glass-button border-white/20 hover:border-white/40"
        )}
        onClick={() => setOpen(true)}
        aria-label={language === 'ar' ? 'فتح البحث المتقدم' : 'Open advanced search'}
      >
        <Search className="h-4 w-4 xl:mr-2" />
        <span className="hidden xl:inline-flex">{t.searchPlaceholder}</span>
        <kbd className="pointer-events-none absolute right-1.5 top-2 hidden h-6 select-none items-center gap-1 rounded border border-white/20 bg-white/10 px-1.5 font-mono text-[10px] font-medium text-white/70 xl:flex">
          <span className="text-xs">⌘</span>K
        </kbd>
      </Button>

      {/* Search Dialog */}
      <CommandDialog open={open} onOpenChange={setOpen}>
        <CommandInput
          placeholder={t.searchPlaceholder}
          value={query}
          onValueChange={setQuery}
          className={isRTL ? 'text-right' : 'text-left'}
        />
        <CommandList>
          <CommandEmpty>{t.noResults}</CommandEmpty>

          {/* Recent Searches */}
          {!query && recentSearches.length > 0 && (
            <CommandGroup heading={t.recentSearches}>
              {recentSearches.map((search, index) => (
                <CommandItem
                  key={index}
                  onSelect={() => setQuery(search)}
                  className="flex items-center gap-2"
                >
                  <Clock className="h-4 w-4 text-white/50" />
                  <span>{search}</span>
                </CommandItem>
              ))}
            </CommandGroup>
          )}

          {/* Search Results */}
          {results.length > 0 && (
            <CommandGroup heading={t.pages}>
              {results.map((result) => {
                const Icon = result.icon
                return (
                  <CommandItem
                    key={result.id}
                    onSelect={() => handleSelect(result)}
                    className="flex items-center gap-3 p-3"
                  >
                    <Icon className="h-4 w-4 text-white/70" />
                    <div className="flex flex-col">
                      <span className="font-medium">
                        {language === 'ar' ? result.titleAr : result.title}
                      </span>
                      <span className="text-xs text-white/50">
                        {language === 'ar' ? result.descriptionAr : result.description}
                      </span>
                    </div>
                    <span className="ml-auto text-xs text-white/40">
                      {language === 'ar' ? result.categoryAr : result.category}
                    </span>
                  </CommandItem>
                )
              })}
            </CommandGroup>
          )}

          {/* Quick Actions */}
          {!query && (
            <>
              <CommandSeparator />
              <CommandGroup heading={t.quickActions}>
                <CommandItem onSelect={() => navigate('/employees/new')}>
                  <Users className="mr-2 h-4 w-4" />
                  <span>{language === 'ar' ? 'إضافة موظف جديد' : 'Add New Employee'}</span>
                </CommandItem>
                <CommandItem onSelect={() => navigate('/projects/new')}>
                  <Briefcase className="mr-2 h-4 w-4" />
                  <span>{language === 'ar' ? 'إنشاء مشروع جديد' : 'Create New Project'}</span>
                </CommandItem>
                <CommandItem onSelect={() => navigate('/reports/generate')}>
                  <FileText className="mr-2 h-4 w-4" />
                  <span>{language === 'ar' ? 'إنشاء تقرير' : 'Generate Report'}</span>
                </CommandItem>
              </CommandGroup>
            </>
          )}
        </CommandList>
      </CommandDialog>
    </>
  )
}

function cn(...classes: string[]): void {
  return classes.filter(Boolean).join(' ')
}
