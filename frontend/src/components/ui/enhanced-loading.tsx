/**
 * UX FIX: Enhanced Loading Components
 * Provides comprehensive loading states with better user feedback
 */

import React, { useState, useEffect } from 'react'
import { Loader2, CheckCircle, AlertCircle, Clock, Wifi, WifiOff } from 'lucide-react'
import { cn } from '@/lib/utils'

// Enhanced loading spinner with progress indication
export interface EnhancedSpinnerProps {
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  variant?: 'default' | 'success' | 'error' | 'warning'
  progress?: number // 0-100
  showProgress?: boolean
  text?: string
  subText?: string
  className?: string
  language?: 'ar' | 'en'
}

export function EnhancedSpinner({
  size = 'md',
  variant = 'default',
  progress,
  showProgress = false,
  text,
  subText,
  className,
  language = 'en'
}: EnhancedSpinnerProps): void {
  const sizeClasses = {
    xs: 'w-3 h-3',
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12'
  }

  const variantClasses = {
    default: 'text-blue-500',
    success: 'text-green-500',
    error: 'text-red-500',
    warning: 'text-yellow-500'
  }

  const icons = {
    default: Loader2,
    success: CheckCircle,
    error: AlertCircle,
    warning: Clock
  }

  const Icon = icons[variant]
  const isRTL = language === 'ar'

  return (
    <div className={cn('flex flex-col items-center space-y-2', className)}>
      <div className="relative">
        <Icon 
          className={cn(
            sizeClasses[size], 
            variantClasses[variant],
            variant === 'default' ? 'animate-spin' : ''
          )} 
        />
        
        {showProgress && progress !== undefined && (
          <div className="absolute inset-0 flex items-center justify-center">
            <svg className={cn(sizeClasses[size])} viewBox="0 0 24 24">
              <circle
                cx="12"
                cy="12"
                r="10"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeDasharray={`${progress * 0.628} 62.8`}
                strokeDashoffset="0"
                className="text-blue-500 transition-all duration-300"
                transform="rotate(-90 12 12)"
              />
            </svg>
          </div>
        )}
      </div>

      {text && (
        <div className={cn('text-center', isRTL ? 'text-right' : 'text-left')}>
          <p className="text-white/80 text-sm font-medium">{text}</p>
          {subText && (
            <p className="text-white/60 text-xs mt-1">{subText}</p>
          )}
          {showProgress && progress !== undefined && (
            <p className="text-white/60 text-xs mt-1">{progress}%</p>
          )}
        </div>
      )}
    </div>
  )
}

// UX FIX: Smart loading overlay with network status
export interface LoadingOverlayProps {
  isLoading: boolean
  children: React.ReactNode
  text?: string
  showNetworkStatus?: boolean
  preserveHeight?: boolean
  minHeight?: number
  className?: string
  language?: 'ar' | 'en'
}

export function LoadingOverlay({
  isLoading,
  children,
  text,
  showNetworkStatus = true,
  preserveHeight = true,
  minHeight = 200,
  className,
  language = 'en'
}: LoadingOverlayProps): void {
  const [isOnline, setIsOnline] = useState(navigator.onLine)
  const [loadingTime, setLoadingTime] = useState(0)

  useEffect(() => {
    const handleOnline = (): any => setIsOnline(true)
    const handleOffline = (): any => setIsOnline(false)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  useEffect(() => {
    let interval: NodeJS.Timeout
    if (isLoading) {
      setLoadingTime(0)
      interval = setInterval(() => {
        setLoadingTime(prev => prev + 1)
      }, 1000)
    }
    return () => clearInterval(interval)
  }, [isLoading])

  const getLoadingText = (): void => {
    if (!isOnline) {
      return language === 'ar' ? 'لا يوجد اتصال بالإنترنت' : 'No internet connection'
    }
    
    if (loadingTime > 10) {
      return language === 'ar' ? 'يستغرق وقتاً أطول من المعتاد...' : 'Taking longer than usual...'
    }
    
    return text || (language === 'ar' ? 'جاري التحميل...' : 'Loading...')
  }

  if (isLoading) {
    return (
      <div 
        className={cn(
          'relative flex items-center justify-center',
          preserveHeight ? `min-h-[${minHeight}px]` : '',
          className
        )}
        style={preserveHeight ? { minHeight } : undefined}
      >
        <div className="absolute inset-0 bg-black/20 backdrop-blur-sm rounded-lg" />
        <div className="relative z-10 flex flex-col items-center space-y-4">
          <EnhancedSpinner
            size="lg"
            variant={!isOnline ? 'error' : 'default'}
            text={getLoadingText()}
            language={language}
          />
          
          {showNetworkStatus && (
            <div className="flex items-center space-x-2 text-white/60 text-xs">
              {isOnline ? (
                <>
                  <Wifi className="w-3 h-3" />
                  <span>{language === 'ar' ? 'متصل' : 'Online'}</span>
                </>
              ) : (
                <>
                  <WifiOff className="w-3 h-3" />
                  <span>{language === 'ar' ? 'غير متصل' : 'Offline'}</span>
                </>
              )}
            </div>
          )}
        </div>
      </div>
    )
  }

  return <>{children}</>
}

// UX FIX: Progressive loading with skeleton states
export interface ProgressiveLoaderProps {
  stages: Array<{
    name: string
    nameAr?: string
    duration?: number
    component?: React.ReactNode
  }>
  currentStage: number
  language?: 'ar' | 'en'
  className?: string
}

export function ProgressiveLoader({
  stages,
  currentStage,
  language = 'en',
  className
}: ProgressiveLoaderProps): void {
  const [completedStages, setCompletedStages] = useState<number[]>([])

  useEffect(() => {
    if (currentStage >= 0 && !completedStages.includes(currentStage)) {
      const timer = setTimeout(() => {
        setCompletedStages(prev => [...prev, currentStage])
      }, stages[currentStage]?.duration || 1000)
      
      return () => clearTimeout(timer)
    }
  }, [currentStage, stages, completedStages])

  return (
    <div className={cn('space-y-4', className)}>
      {stages.map((stage, index) => {
        const isActive = index === currentStage
        const isCompleted = completedStages.includes(index)
        const stageName = language === 'ar' && stage.nameAr ? stage.nameAr : stage.name

        return (
          <div key={index} className="flex items-center space-x-3">
            <div className={cn(
              'w-6 h-6 rounded-full flex items-center justify-center transition-all duration-300',
              isCompleted ? 'bg-green-500' : isActive ? 'bg-blue-500' : 'bg-white/20'
            )}>
              {isCompleted ? (
                <CheckCircle className="w-4 h-4 text-white" />
              ) : isActive ? (
                <Loader2 className="w-4 h-4 text-white animate-spin" />
              ) : (
                <div className="w-2 h-2 bg-white/60 rounded-full" />
              )}
            </div>
            
            <div className="flex-1">
              <p className={cn(
                'text-sm transition-colors duration-300',
                isCompleted ? 'text-green-400' : isActive ? 'text-white' : 'text-white/60'
              )}>
                {stageName}
              </p>
              
              {isActive && stage.component && (
                <div className="mt-2">
                  {stage.component}
                </div>
              )}
            </div>
          </div>
        )
      })}
    </div>
  )
}

// UX FIX: Smart button with loading states
export interface SmartButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  isLoading?: boolean
  loadingText?: string
  loadingTextAr?: string
  success?: boolean
  successText?: string
  successTextAr?: string
  error?: boolean
  errorText?: string
  errorTextAr?: string
  language?: 'ar' | 'en'
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
  size?: 'default' | 'sm' | 'lg' | 'icon'
}

export function SmartButton({
  children,
  isLoading = false,
  loadingText,
  loadingTextAr,
  success = false,
  successText,
  successTextAr,
  error = false,
  errorText,
  errorTextAr,
  language = 'en',
  className,
  disabled,
  ...props
}: SmartButtonProps): void {
  const [showSuccess, setShowSuccess] = useState(false)
  const [showError, setShowError] = useState(false)

  useEffect(() => {
    if (success) {
      setShowSuccess(true)
      const timer = setTimeout(() => setShowSuccess(false), 2000)
      return () => clearTimeout(timer)
    }
  }, [success])

  useEffect(() => {
    if (error) {
      setShowError(true)
      const timer = setTimeout(() => setShowError(false), 3000)
      return () => clearTimeout(timer)
    }
  }, [error])

  const getButtonContent = (): void => {
    if (isLoading) {
      const text = language === 'ar' && loadingTextAr ? loadingTextAr : loadingText
      return (
        <>
          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
          {text || (language === 'ar' ? 'جاري المعالجة...' : 'Processing...')}
        </>
      )
    }

    if (showSuccess) {
      const text = language === 'ar' && successTextAr ? successTextAr : successText
      return (
        <>
          <CheckCircle className="w-4 h-4 mr-2" />
          {text || (language === 'ar' ? 'تم بنجاح' : 'Success')}
        </>
      )
    }

    if (showError) {
      const text = language === 'ar' && errorTextAr ? errorTextAr : errorText
      return (
        <>
          <AlertCircle className="w-4 h-4 mr-2" />
          {text || (language === 'ar' ? 'حدث خطأ' : 'Error')}
        </>
      )
    }

    return children
  }

  return (
    <button
      {...props}
      disabled={disabled || isLoading}
      className={cn(
        'glass-button transition-all duration-200',
        showSuccess && 'bg-green-500/30 border-green-500/50',
        showError && 'bg-red-500/30 border-red-500/50',
        className
      )}
    >
      {getButtonContent()}
    </button>
  )
}

export default {
  EnhancedSpinner,
  LoadingOverlay,
  ProgressiveLoader,
  SmartButton
}
