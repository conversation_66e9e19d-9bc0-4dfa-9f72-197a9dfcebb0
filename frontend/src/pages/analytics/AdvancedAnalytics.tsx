import React from 'react';
import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { apiClient } from '@/services/api'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area
} from 'recharts'
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Download,
  Filter,
  Calendar,
  Users,
  DollarSign,
  Target,
  Activity,
  Zap,
  Eye,
  RefreshCw,
  Settings,
  Share,
  Bookmark,
  AlertTriangle,
  CheckCircle,
  Clock,
  ArrowUpRight,
  ArrowDownRight,
  PieChart as PieChartIcon,
  Package
} from 'lucide-react'

interface AdvancedAnalyticsProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    analytics: 'التحليلات المتقدمة',
    dashboard: 'لوحة التحليلات',
    realTimeData: 'البيانات المباشرة',
    performanceMetrics: 'مقاييس الأداء',
    businessIntelligence: 'ذكاء الأعمال',
    predictiveAnalytics: 'التحليلات التنبؤية',
    customReports: 'التقارير المخصصة',
    dataVisualization: 'تصور البيانات',
    kpiTracking: 'تتبع مؤشرات الأداء',
    trendAnalysis: 'تحليل الاتجاهات',
    comparativeAnalysis: 'التحليل المقارن',
    forecastingModels: 'نماذج التنبؤ',
    dataInsights: 'رؤى البيانات',
    actionableRecommendations: 'توصيات قابلة للتنفيذ',
    export: 'تصدير',
    filter: 'تصفية',
    refresh: 'تحديث',
    share: 'مشاركة',
    bookmark: 'إشارة مرجعية',
    settings: 'الإعدادات',
    viewDetails: 'عرض التفاصيل',
    // Metrics
    totalRevenue: 'إجمالي الإيرادات',
    profitMargin: 'هامش الربح',
    customerSatisfaction: 'رضا العملاء',
    employeeProductivity: 'إنتاجية الموظفين',
    operationalEfficiency: 'الكفاءة التشغيلية',
    marketShare: 'حصة السوق',
    growthRate: 'معدل النمو',
    costReduction: 'تقليل التكاليف',
    qualityScore: 'نقاط الجودة',
    innovationIndex: 'مؤشر الابتكار',
    // Time periods
    today: 'اليوم',
    thisWeek: 'هذا الأسبوع',
    thisMonth: 'هذا الشهر',
    thisQuarter: 'هذا الربع',
    thisYear: 'هذا العام',
    lastMonth: 'الشهر الماضي',
    lastQuarter: 'الربع الماضي',
    lastYear: 'العام الماضي',
    // Status
    excellent: 'ممتاز',
    good: 'جيد',
    average: 'متوسط',
    belowAverage: 'أقل من المتوسط',
    critical: 'حرج',
    improving: 'يتحسن',
    declining: 'يتراجع',
    stable: 'مستقر'
  },
  en: {
    analytics: 'Advanced Analytics',
    dashboard: 'Analytics Dashboard',
    realTimeData: 'Real-time Data',
    performanceMetrics: 'Performance Metrics',
    businessIntelligence: 'Business Intelligence',
    predictiveAnalytics: 'Predictive Analytics',
    customReports: 'Custom Reports',
    dataVisualization: 'Data Visualization',
    kpiTracking: 'KPI Tracking',
    trendAnalysis: 'Trend Analysis',
    comparativeAnalysis: 'Comparative Analysis',
    forecastingModels: 'Forecasting Models',
    dataInsights: 'Data Insights',
    actionableRecommendations: 'Actionable Recommendations',
    export: 'Export',
    filter: 'Filter',
    refresh: 'Refresh',
    share: 'Share',
    bookmark: 'Bookmark',
    settings: 'Settings',
    viewDetails: 'View Details',
    // Metrics
    totalRevenue: 'Total Revenue',
    profitMargin: 'Profit Margin',
    customerSatisfaction: 'Customer Satisfaction',
    employeeProductivity: 'Employee Productivity',
    operationalEfficiency: 'Operational Efficiency',
    marketShare: 'Market Share',
    growthRate: 'Growth Rate',
    costReduction: 'Cost Reduction',
    qualityScore: 'Quality Score',
    innovationIndex: 'Innovation Index',
    // Time periods
    today: 'Today',
    thisWeek: 'This Week',
    thisMonth: 'This Month',
    thisQuarter: 'This Quarter',
    thisYear: 'This Year',
    lastMonth: 'Last Month',
    lastQuarter: 'Last Quarter',
    lastYear: 'Last Year',
    // Status
    excellent: 'Excellent',
    good: 'Good',
    average: 'Average',
    belowAverage: 'Below Average',
    critical: 'Critical',
    improving: 'Improving',
    declining: 'Declining',
    stable: 'Stable'
  }
}

export default function AdvancedAnalytics({ language }: AdvancedAnalyticsProps): React.ReactElement {
  const [selectedPeriod, setSelectedPeriod] = useState('thisMonth')
  const [activeTab, setActiveTab] = useState('overview')
  const [refreshing, setRefreshing] = useState(false)
  const t = translations[language]
  const isRTL = language === 'ar'

  // Real KPI data from API
  const [kpiMetrics, setKpiMetrics] = useState<any[]>([])
  const [loadingKpis, setLoadingKpis] = useState(true)

  // Fetch real KPI data
  useEffect(() => {
    const fetchKPIData = async () => {
      try {
        setLoadingKpis(true)
        const response = await apiClient.get('/kpi-metrics/')
        const data = response.data as any
        const kpis = data.results || data

        // Transform real KPI data to component format
        const transformedKpis = kpis.map((kpi: any) => ({
          title: language === 'ar' ? kpi.name_ar || kpi.name : kpi.name,
          value: kpi.current_value || 0,
          change: kpi.change_percentage ? `${kpi.change_percentage > 0 ? '+' : ''}${kpi.change_percentage.toFixed(1)}%` : '0%',
          trend: kpi.trend || 'stable',
          status: getKPIStatus(kpi.current_value, kpi.target_value, kpi.warning_threshold, kpi.critical_threshold),
          icon: getKPIIcon(kpi.metric_type),
          color: getKPIColor(kpi.metric_type),
          target: kpi.target_value || 0,
          achievement: kpi.achievement_percentage || 0,
          unit: kpi.unit || '',
          category: kpi.category_name || 'General'
        }))

        setKpiMetrics(transformedKpis)
      } catch (error) {
        console.error('Error fetching KPI data:', error)
        // Set empty array instead of mock data
        setKpiMetrics([])
      } finally {
        setLoadingKpis(false)
      }
    }

    fetchKPIData()
  }, [language])

  // Helper functions for KPI transformation
  const getKPIStatus = (current: number, target: number, warning: number, critical: number): void => {
    if (!current || !target) return 'no-data'
    const percentage = (current / target) * 100
    if (percentage >= 90) return 'excellent'
    if (percentage >= 70) return 'good'
    if (percentage >= 50) return 'average'
    return 'poor'
  }

  const getKPIIcon = (type: string): void => {
    switch (type) {
      case 'FINANCIAL': return DollarSign
      case 'OPERATIONAL': return Activity
      case 'CUSTOMER': return Users
      default: return Activity
    }
  }

  const getKPIColor = (type: string): void => {
    switch (type) {
      case 'FINANCIAL': return 'from-green-500 to-green-600'
      case 'OPERATIONAL': return 'from-blue-500 to-blue-600'
      case 'CUSTOMER': return 'from-purple-500 to-purple-600'
      default: return 'from-gray-500 to-gray-600'
    }
  }

  // Show loading state for KPIs
  if (loadingKpis) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg text-white">
          {language === 'ar' ? 'جاري تحميل مؤشرات الأداء...' : 'Loading KPIs...'}
        </div>
      </div>
    )
  }

  // Show message if no KPIs available
  if (kpiMetrics.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center text-white">
          <div className="text-lg mb-2">
            {language === 'ar' ? 'لا توجد مؤشرات أداء متاحة' : 'No KPIs Available'}
          </div>
          <div className="text-sm opacity-70">
            {language === 'ar'
              ? 'يرجى إضافة مؤشرات أداء من صفحة إدارة المؤشرات'
              : 'Please add KPIs from the KPI Management page'}
          </div>
        </div>
      </div>
    )
  }

  // TODO: Replace with real insights from AI/ML analysis of KPI data
  const insights = kpiMetrics.length > 0 ? [
    {
      type: 'info',
      title: language === 'ar' ? 'تحليل البيانات الحقيقية' : 'Real Data Analysis',
      description: language === 'ar'
        ? `تم تحميل ${kpiMetrics.length} مؤشر أداء من قاعدة البيانات`
        : `Loaded ${kpiMetrics.length} KPIs from database`,
      impact: 'متوسط',
      effort: 'منخفض',
      timeline: 'مستمر',
      icon: BarChart3,
      color: 'from-blue-500 to-blue-600'
    }
  ] : []

  // TODO: Replace with real predictive models from ML service
  const predictiveModels = kpiMetrics.length > 0 ? [
    {
      name: language === 'ar' ? 'نموذج تحليل البيانات الحقيقية' : 'Real Data Analysis Model',
      accuracy: 100,
      prediction: language === 'ar'
        ? `تحليل ${kpiMetrics.length} مؤشر أداء حقيقي`
        : `Analysis of ${kpiMetrics.length} real KPIs`,
      confidence: language === 'ar' ? 'عالي' : 'High',
      lastUpdated: new Date().toISOString().split('T')[0]
    }
  ] : []

  const handleRefresh = async () => {
    setRefreshing(true)
    await new Promise(resolve => setTimeout(resolve, 1000))
    setRefreshing(false)
  }

  const getTrendIcon = (trend: string): void => {
    return trend === 'up' ? (
      <ArrowUpRight className="h-4 w-4 text-green-400" />
    ) : trend === 'down' ? (
      <ArrowDownRight className="h-4 w-4 text-red-400" />
    ) : (
      <Activity className="h-4 w-4 text-blue-400" />
    )
  }

  const getStatusColor = (status: string): void => {
    switch (status) {
      case 'excellent':
        return 'text-green-400'
      case 'good':
        return 'text-blue-400'
      case 'average':
        return 'text-yellow-400'
      case 'belowAverage':
        return 'text-orange-400'
      case 'critical':
        return 'text-red-400'
      default:
        return 'text-gray-400'
    }
  }

  const getInsightTypeIcon = (type: string): void => {
    switch (type) {
      case 'opportunity':
        return <TrendingUp className="h-5 w-5 text-green-400" />
      case 'risk':
        return <AlertTriangle className="h-5 w-5 text-red-400" />
      case 'trend':
        return <Activity className="h-5 w-5 text-blue-400" />
      default:
        return <Eye className="h-5 w-5 text-gray-400" />
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">{t.analytics}</h1>
          <p className="text-white/70">تحليلات متقدمة ورؤى ذكية لاتخاذ قرارات مدروسة</p>
        </div>

        <div className="flex items-center gap-3">
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="glass-input"
          >
            <option value="today">{t.today}</option>
            <option value="thisWeek">{t.thisWeek}</option>
            <option value="thisMonth">{t.thisMonth}</option>
            <option value="thisQuarter">{t.thisQuarter}</option>
            <option value="thisYear">{t.thisYear}</option>
          </select>
          <Button
            onClick={handleRefresh}
            disabled={refreshing}
            className="glass-button"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            {t.refresh}
          </Button>
          <Button variant="outline" className="glass-button">
            <Download className="h-4 w-4 mr-2" />
            {t.export}
          </Button>
          <Button variant="outline" className="glass-button">
            <Share className="h-4 w-4 mr-2" />
            {t.share}
          </Button>
        </div>
      </div>

      {/* KPI Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {kpiMetrics.map((metric, index) => (
          <Card key={index} className="glass-card border-white/20 hover:border-white/40 transition-all">
            <CardContent className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <p className="text-white/70 text-sm mb-1">{metric.title}</p>
                  <p className="text-2xl font-bold text-white mb-2">{metric.value}</p>
                  <div className="flex items-center gap-2">
                    {getTrendIcon(metric.trend)}
                    <span className={`text-sm font-medium ${
                      metric.trend === 'up' ? 'text-green-400' :
                      metric.trend === 'down' ? 'text-red-400' : 'text-blue-400'
                    }`}>
                      {metric.change}
                    </span>
                  </div>
                </div>
                <div className={`p-3 rounded-xl bg-gradient-to-r ${metric.color} shadow-lg`}>
                  <metric.icon className="h-6 w-6 text-white" />
                </div>
              </div>

              {/* Progress to Target */}
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-white/70">Target: {metric.target}</span>
                  <span className={`font-medium ${getStatusColor(metric.status)}`}>
                    {metric.achievement}%
                  </span>
                </div>
                <div className="w-full bg-white/20 rounded-full h-2">
                  <div
                    className={`bg-gradient-to-r ${metric.color} h-2 rounded-full transition-all duration-1000`}
                    style={{ width: `${Math.min(metric.achievement, 100)}%` }}
                  ></div>
                </div>
                <div className="text-xs text-white/60">
                  Status: <span className={getStatusColor(metric.status)}>{t[metric.status as keyof typeof t]}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Analytics Tabs */}
      <div className="flex space-x-1 bg-white/10 p-1 rounded-lg">
        {['overview', 'insights', 'predictions', 'reports'].map((tab) => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab)}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all ${
              activeTab === tab
                ? 'bg-white/20 text-white shadow-lg'
                : 'text-white/70 hover:text-white hover:bg-white/10'
            }`}
          >
            {tab === 'overview' && 'نظرة عامة'}
            {tab === 'insights' && 'الرؤى'}
            {tab === 'predictions' && 'التنبؤات'}
            {tab === 'reports' && 'التقارير'}
          </button>
        ))}
      </div>

      {/* Content based on active tab */}
      {activeTab === 'insights' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {insights.map((insight, index) => (
            <Card key={index} className="glass-card border-white/20">
              <CardContent className="p-6">
                <div className="flex items-start gap-4">
                  <div className={`p-3 rounded-xl bg-gradient-to-r ${insight.color} shadow-lg`}>
                    {getInsightTypeIcon(insight.type)}
                  </div>
                  <div className="flex-1">
                    <h3 className="text-white font-semibold mb-2">{insight.title}</h3>
                    <p className="text-white/80 text-sm mb-4">{insight.description}</p>

                    <div className="grid grid-cols-3 gap-2 text-xs">
                      <div>
                        <p className="text-white/60">التأثير</p>
                        <p className="text-white font-medium">{insight.impact}</p>
                      </div>
                      <div>
                        <p className="text-white/60">الجهد</p>
                        <p className="text-white font-medium">{insight.effort}</p>
                      </div>
                      <div>
                        <p className="text-white/60">الوقت</p>
                        <p className="text-white font-medium">{insight.timeline}</p>
                      </div>
                    </div>

                    <Button size="sm" className="glass-button mt-4 w-full">
                      {t.viewDetails}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {activeTab === 'predictions' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {predictiveModels.map((model, index) => (
            <Card key={index} className="glass-card border-white/20">
              <CardContent className="p-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-white font-semibold">{model.name}</h3>
                    <div className="flex items-center gap-1">
                      <Activity className="h-4 w-4 text-blue-400" />
                      <span className="text-blue-400 text-sm font-medium">{model.accuracy}%</span>
                    </div>
                  </div>

                  <div className="bg-white/5 rounded-lg p-4">
                    <p className="text-white/90 text-sm">{model.prediction}</p>
                  </div>

                  <div className="flex justify-between text-sm">
                    <div>
                      <p className="text-white/60">الثقة</p>
                      <p className="text-white font-medium">{model.confidence}</p>
                    </div>
                    <div>
                      <p className="text-white/60">آخر تحديث</p>
                      <p className="text-white font-medium">{model.lastUpdated}</p>
                    </div>
                  </div>

                  <div className="w-full bg-white/20 rounded-full h-2">
                    <div
                      className="bg-gradient-to-r from-blue-500 to-green-500 h-2 rounded-full"
                      style={{ width: `${model.accuracy}%` }}
                    ></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Real-time Data Visualization Placeholder */}
      {activeTab === 'overview' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="glass-card border-white/20">
            <CardHeader>
              <CardTitle className="text-white">اتجاهات الأداء</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-64 flex items-center justify-center">
                <div className="text-center">
                  <LineChart className="h-16 w-16 text-white/40 mx-auto mb-4" />
                  <p className="text-white/60">مخطط الاتجاهات سيظهر هنا</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="glass-card border-white/20">
            <CardHeader>
              <CardTitle className="text-white">توزيع الموارد</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-64 flex items-center justify-center">
                <div className="text-center">
                  <PieChart className="h-16 w-16 text-white/40 mx-auto mb-4" />
                  <p className="text-white/60">مخطط دائري سيظهر هنا</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
