/**
 * Suppliers Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  Building,
  Phone,
  Mail,
  MapPin,
  Star,
  Eye,
  Edit,
  Trash2,
  User,
  Calendar,
  DollarSign,
  Truck,
  Factory,
  ShoppingCart
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { supplierService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface SuppliersProps {
  language: 'ar' | 'en'
}

interface Supplier {
  id: number
  name: string
  nameEn: string
  contactPerson: string
  phone: string
  email: string
  address: string
  category: string
  rating: number
  status: 'active' | 'inactive' | 'pending'
  contractValue: number
  lastOrder: string
  joinDate: string
  orders: number
  performance: 'excellent' | 'good' | 'average' | 'poor'
}

const translations = {
  ar: {
    suppliers: 'الموردون',
    addSupplier: 'إضافة مورد',
    editSupplier: 'تعديل المورد',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذا المورد؟',
    searchPlaceholder: 'البحث في الموردين...',
    supplierName: 'اسم المورد',
    contactPerson: 'الشخص المسؤول',
    phone: 'الهاتف',
    email: 'البريد الإلكتروني',
    address: 'العنوان',
    category: 'الفئة',
    rating: 'التقييم',
    status: 'الحالة',
    contractValue: 'قيمة العقد',
    lastOrder: 'آخر طلب',
    joinDate: 'تاريخ الانضمام',
    orders: 'الطلبات',
    performance: 'الأداء',
    active: 'نشط',
    inactive: 'غير نشط',
    pending: 'معلق',
    excellent: 'ممتاز',
    good: 'جيد',
    average: 'متوسط',
    poor: 'ضعيف',
    categories: {
      technology: 'تقنية',
      furniture: 'أثاث',
      maintenance: 'صيانة',
      office: 'مكتبية',
      catering: 'تموين',
      cleaning: 'تنظيف'
    }
  },
  en: {
    suppliers: 'Suppliers',
    addSupplier: 'Add Supplier',
    editSupplier: 'Edit Supplier',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this supplier?',
    searchPlaceholder: 'Search suppliers...',
    supplierName: 'Supplier Name',
    contactPerson: 'Contact Person',
    phone: 'Phone',
    email: 'Email',
    address: 'Address',
    category: 'Category',
    rating: 'Rating',
    status: 'Status',
    contractValue: 'Contract Value',
    lastOrder: 'Last Order',
    joinDate: 'Join Date',
    orders: 'Orders',
    performance: 'Performance',
    active: 'Active',
    inactive: 'Inactive',
    pending: 'Pending',
    excellent: 'Excellent',
    good: 'Good',
    average: 'Average',
    poor: 'Poor',
    categories: {
      technology: 'Technology',
      furniture: 'Furniture',
      maintenance: 'Maintenance',
      office: 'Office',
      catering: 'Catering',
      cleaning: 'Cleaning'
    }
  }
}

export default function Suppliers({ language }: SuppliersProps): React.ReactElement {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: suppliers,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<Supplier>({
    service: supplierService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getCategoryIcon = (category: string): void => {
    switch (category) {
      case 'technology':
        return <Factory className="h-4 w-4" />
      case 'furniture':
        return <Building className="h-4 w-4" />
      case 'maintenance':
        return <Truck className="h-4 w-4" />
      case 'office':
        return <ShoppingCart className="h-4 w-4" />
      case 'catering':
        return <User className="h-4 w-4" />
      case 'cleaning':
        return <Building className="h-4 w-4" />
      default:
        return <Building className="h-4 w-4" />
    }
  }

  const getStatusColor = (status: string): void => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'inactive':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getPerformanceColor = (performance: string): void => {
    switch (performance) {
      case 'excellent':
        return 'text-green-400'
      case 'good':
        return 'text-blue-400'
      case 'average':
        return 'text-yellow-400'
      case 'poor':
        return 'text-red-400'
      default:
        return 'text-gray-400'
    }
  }

  // Table columns configuration
  const columns: TableColumn<Supplier>[] = [
    {
      key: 'name',
      label: t.supplierName,
      sortable: true,
      render: (item: Supplier) => (
        <div className="flex items-center gap-2">
          {getCategoryIcon(item.category)}
          <div>
            <div className="font-medium text-white">
              {language === 'ar' ? item.name : item.nameEn}
            </div>
            <div className="text-sm text-white/60">{item.contactPerson}</div>
          </div>
        </div>
      )
    },
    {
      key: 'category',
      label: t.category,
      sortable: true,
      render: (item: Supplier) => (
        <Badge variant="outline" className="text-white border-white/20">
          {t.categories[item.category as keyof typeof t.categories] || item.category}
        </Badge>
      )
    },
    {
      key: 'phone',
      label: t.phone,
      render: (item: Supplier) => (
        <div className="flex items-center gap-1">
          <Phone className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">{item.phone}</span>
        </div>
      )
    },
    {
      key: 'email',
      label: t.email,
      render: (item: Supplier) => (
        <div className="flex items-center gap-1">
          <Mail className="h-3 w-3 text-green-400" />
          <span className="text-white/80">{item.email}</span>
        </div>
      )
    },
    {
      key: 'rating',
      label: t.rating,
      sortable: true,
      render: (item: Supplier) => (
        <div className="flex items-center gap-1">
          <Star className="h-3 w-3 text-yellow-400 fill-current" />
          <span className="text-white font-medium">{item.rating}</span>
        </div>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: Supplier) => (
        <Badge className={getStatusColor(item.status)}>
          {String(t[item.status as keyof typeof t])}
        </Badge>
      )
    },
    {
      key: 'contractValue',
      label: t.contractValue,
      sortable: true,
      render: (item: Supplier) => (
        <span className="text-white font-medium">
          {new Intl.NumberFormat('ar-SA', {
            style: 'currency',
            currency: 'SAR'
          }).format(item.contractValue)}
        </span>
      )
    },
    {
      key: 'performance',
      label: t.performance,
      render: (item: Supplier) => (
        <span className={`font-medium ${getPerformanceColor(item.performance)}`}>
          {String(t[item.performance as keyof typeof t])}
        </span>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction<Supplier>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: Supplier) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: Supplier) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: Supplier) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'category',
      label: t.category,
      options: [
        { label: t.categories.technology, value: 'technology' },
        { label: t.categories.furniture, value: 'furniture' },
        { label: t.categories.maintenance, value: 'maintenance' },
        { label: t.categories.office, value: 'office' },
        { label: t.categories.catering, value: 'catering' },
        { label: t.categories.cleaning, value: 'cleaning' }
      ]
    },
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.active, value: 'active' },
        { label: t.inactive, value: 'inactive' },
        { label: t.pending, value: 'pending' }
      ]
    },
    {
      key: 'performance',
      label: t.performance,
      options: [
        { label: t.excellent, value: 'excellent' },
        { label: t.good, value: 'good' },
        { label: t.average, value: 'average' },
        { label: t.poor, value: 'poor' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'name',
      label: t.supplierName,
      type: 'text',
      required: true
    },
    {
      name: 'nameEn',
      label: t.supplierName + ' (English)',
      type: 'text',
      required: true
    },
    {
      name: 'contactPerson',
      label: t.contactPerson,
      type: 'text',
      required: true
    },
    {
      name: 'phone',
      label: t.phone,
      type: 'tel',
      required: true
    },
    {
      name: 'email',
      label: t.email,
      type: 'email',
      required: true
    },
    {
      name: 'address',
      label: t.address,
      type: 'textarea',
      required: true
    },
    {
      name: 'category',
      label: t.category,
      type: 'select',
      required: true,
      options: [
        { label: t.categories.technology, value: 'technology' },
        { label: t.categories.furniture, value: 'furniture' },
        { label: t.categories.maintenance, value: 'maintenance' },
        { label: t.categories.office, value: 'office' },
        { label: t.categories.catering, value: 'catering' },
        { label: t.categories.cleaning, value: 'cleaning' }
      ]
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: t.active, value: 'active' },
        { label: t.inactive, value: 'inactive' },
        { label: t.pending, value: 'pending' }
      ]
    },
    {
      name: 'rating',
      label: t.rating,
      type: 'number',
      min: 0,
      max: 5,
      step: 0.1
    },
    {
      name: 'contractValue',
      label: t.contractValue,
      type: 'number',
      min: 0
    },
    {
      name: 'performance',
      label: t.performance,
      type: 'select',
      options: [
        { label: t.excellent, value: 'excellent' },
        { label: t.good, value: 'good' },
        { label: t.average, value: 'average' },
        { label: t.poor, value: 'poor' }
      ]
    }
  ]

  // Event handlers
  const handleCreate = (): void => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = (): void => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<Supplier>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('csv')
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.suppliers}
        data={suppliers}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addSupplier : modalMode === 'edit' ? t.editSupplier : t.view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
