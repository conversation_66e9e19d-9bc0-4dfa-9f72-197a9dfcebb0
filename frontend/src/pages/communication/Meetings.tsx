/**
 * Meetings Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  Video,
  Calendar,
  Clock,
  Users,
  MapPin,
  Eye,
  Edit,
  Trash2,
  Monitor,
  User,
  CheckCircle,
  <PERSON><PERSON><PERSON>riangle,
  PlayCircle,
  Link
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { meetingService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface MeetingsProps {
  language: 'ar' | 'en'
}

interface Meeting {
  id: number
  title: string
  titleAr: string
  description: string
  descriptionAr: string
  dateTime: string
  duration: number
  location: string
  locationAr: string
  type: 'inPerson' | 'virtual' | 'hybrid'
  status: 'scheduled' | 'ongoing' | 'completed' | 'cancelled'
  organizer: string
  attendees: number
  agenda: string
  agendaAr: string
  meetingLink?: string
  hasRecording: boolean
  notes?: string
}

const translations = {
  ar: {
    meetings: 'الاجتماعات',
    addMeeting: 'إضافة اجتماع',
    editMeeting: 'تعديل الاجتماع',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذا الاجتماع؟',
    searchPlaceholder: 'البحث في الاجتماعات...',
    title: 'عنوان الاجتماع',
    description: 'الوصف',
    dateTime: 'التاريخ والوقت',
    duration: 'المدة (بالدقائق)',
    location: 'المكان',
    type: 'النوع',
    status: 'الحالة',
    organizer: 'المنظم',
    attendees: 'عدد الحاضرين',
    agenda: 'جدول الأعمال',
    meetingLink: 'رابط الاجتماع',
    hasRecording: 'يحتوي على تسجيل',
    notes: 'الملاحظات',
    scheduled: 'مجدول',
    ongoing: 'جاري',
    completed: 'مكتمل',
    cancelled: 'ملغي',
    inPerson: 'حضوري',
    virtual: 'افتراضي',
    hybrid: 'مختلط',
    minutes: 'دقيقة'
  },
  en: {
    meetings: 'Meetings',
    addMeeting: 'Add Meeting',
    editMeeting: 'Edit Meeting',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this meeting?',
    searchPlaceholder: 'Search meetings...',
    title: 'Meeting Title',
    description: 'Description',
    dateTime: 'Date & Time',
    duration: 'Duration (minutes)',
    location: 'Location',
    type: 'Type',
    status: 'Status',
    organizer: 'Organizer',
    attendees: 'Attendees Count',
    agenda: 'Agenda',
    meetingLink: 'Meeting Link',
    hasRecording: 'Has Recording',
    notes: 'Notes',
    scheduled: 'Scheduled',
    ongoing: 'Ongoing',
    completed: 'Completed',
    cancelled: 'Cancelled',
    inPerson: 'In-Person',
    virtual: 'Virtual',
    hybrid: 'Hybrid',
    minutes: 'minutes'
  }
}

export default function Meetings({ language }: MeetingsProps): React.ReactElement {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: meetings,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<Meeting>({
    service: meetingService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getStatusIcon = (status: string): void => {
    switch (status) {
      case 'scheduled':
        return <Clock className="h-4 w-4 text-blue-500" />
      case 'ongoing':
        return <PlayCircle className="h-4 w-4 text-green-500" />
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-gray-500" />
      case 'cancelled':
        return <AlertTriangle className="h-4 w-4 text-red-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusColor = (status: string): void => {
    switch (status) {
      case 'scheduled':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'ongoing':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'completed':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getTypeIcon = (type: string): void => {
    switch (type) {
      case 'virtual':
        return <Monitor className="h-4 w-4 text-blue-400" />
      case 'inPerson':
        return <MapPin className="h-4 w-4 text-green-400" />
      case 'hybrid':
        return <Video className="h-4 w-4 text-purple-400" />
      default:
        return <Video className="h-4 w-4 text-gray-400" />
    }
  }

  const formatDuration = (minutes: number): string => {
    return `${minutes} ${t.minutes}`
  }

  const formatDateTime = (dateTime: string): string => {
    const date = new Date(dateTime)
    return date.toLocaleString(language === 'ar' ? 'ar-SA' : 'en-US', {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  // Table columns configuration
  const columns: TableColumn<Meeting>[] = [
    {
      key: 'title',
      label: t.title,
      sortable: true,
      render: (item: Meeting) => (
        <div className="flex items-center gap-2">
          {getTypeIcon(item.type)}
          <div>
            <div className="font-medium text-white">
              {language === 'ar' ? item.titleAr : item.title}
            </div>
            <div className="text-sm text-white/60">
              {language === 'ar' ? item.descriptionAr?.substring(0, 50) + '...' : item.description?.substring(0, 50) + '...'}
            </div>
          </div>
          {item.hasRecording && <Video className="h-4 w-4 text-red-400" />}
        </div>
      )
    },
    {
      key: 'dateTime',
      label: t.dateTime,
      sortable: true,
      render: (item: Meeting) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">{formatDateTime(item.dateTime)}</span>
        </div>
      )
    },
    {
      key: 'duration',
      label: t.duration,
      sortable: true,
      render: (item: Meeting) => (
        <div className="flex items-center gap-1">
          <Clock className="h-3 w-3 text-purple-400" />
          <span className="text-white/80">{formatDuration(item.duration)}</span>
        </div>
      )
    },
    {
      key: 'type',
      label: t.type,
      sortable: true,
      render: (item: Meeting) => (
        <Badge variant="outline" className="text-white border-white/20">
          {t[item.type as keyof typeof t]}
        </Badge>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: Meeting) => (
        <div className="flex items-center gap-1">
          {getStatusIcon(item.status)}
          <Badge className={getStatusColor(item.status)}>
            {t[item.status as keyof typeof t]}
          </Badge>
        </div>
      )
    },
    {
      key: 'organizer',
      label: t.organizer,
      render: (item: Meeting) => (
        <div className="flex items-center gap-1">
          <User className="h-3 w-3 text-green-400" />
          <span className="text-white/80">{item.organizer}</span>
        </div>
      )
    },
    {
      key: 'attendees',
      label: t.attendees,
      sortable: true,
      render: (item: Meeting) => (
        <div className="flex items-center gap-1">
          <Users className="h-3 w-3 text-orange-400" />
          <span className="text-white font-medium">{item.attendees}</span>
        </div>
      )
    },
    {
      key: 'location',
      label: t.location,
      render: (item: Meeting) => (
        <div className="flex items-center gap-1">
          <MapPin className="h-3 w-3 text-red-400" />
          <span className="text-white/80">
            {language === 'ar' ? item.locationAr : item.location}
          </span>
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction<Meeting>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: Meeting) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: Meeting) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: Meeting) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'type',
      label: t.type,
      options: [
        { label: t.inPerson, value: 'inPerson' },
        { label: t.virtual, value: 'virtual' },
        { label: t.hybrid, value: 'hybrid' }
      ]
    },
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.scheduled, value: 'scheduled' },
        { label: t.ongoing, value: 'ongoing' },
        { label: t.completed, value: 'completed' },
        { label: t.cancelled, value: 'cancelled' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'title',
      label: t.title,
      type: 'text',
      required: true
    },
    {
      name: 'titleAr',
      label: t.title + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'description',
      label: t.description,
      type: 'textarea',
      required: true
    },
    {
      name: 'descriptionAr',
      label: t.description + ' (عربي)',
      type: 'textarea',
      required: true
    },
    {
      name: 'dateTime',
      label: t.dateTime,
      type: 'datetime-local',
      required: true
    },
    {
      name: 'duration',
      label: t.duration,
      type: 'number',
      required: true
    },
    {
      name: 'location',
      label: t.location,
      type: 'text',
      required: true
    },
    {
      name: 'locationAr',
      label: t.location + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'type',
      label: t.type,
      type: 'select',
      required: true,
      options: [
        { label: t.inPerson, value: 'inPerson' },
        { label: t.virtual, value: 'virtual' },
        { label: t.hybrid, value: 'hybrid' }
      ]
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: t.scheduled, value: 'scheduled' },
        { label: t.ongoing, value: 'ongoing' },
        { label: t.completed, value: 'completed' },
        { label: t.cancelled, value: 'cancelled' }
      ]
    },
    {
      name: 'organizer',
      label: t.organizer,
      type: 'text',
      required: true
    },
    {
      name: 'attendees',
      label: t.attendees,
      type: 'number',
      required: true
    },
    {
      name: 'agenda',
      label: t.agenda,
      type: 'textarea',
      required: true
    },
    {
      name: 'agendaAr',
      label: t.agenda + ' (عربي)',
      type: 'textarea',
      required: true
    },
    {
      name: 'meetingLink',
      label: t.meetingLink,
      type: 'text'
    },
    {
      name: 'hasRecording',
      label: t.hasRecording,
      type: 'checkbox'
    },
    {
      name: 'notes',
      label: t.notes,
      type: 'textarea'
    }
  ]

  // Event handlers
  const handleCreate = (): void => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = (): void => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<Meeting>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('csv')
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.meetings}
        data={meetings}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addMeeting : modalMode === 'edit' ? t.editMeeting : t.view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
