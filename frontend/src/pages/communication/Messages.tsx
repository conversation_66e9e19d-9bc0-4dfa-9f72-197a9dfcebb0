/**
 * Messages Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  MessageSquare,
  Send,
  User,
  Calendar,
  Clock,
  CheckCheck,
  Eye,
  Edit,
  Trash2,
  Star,
  Archive,
  Users,
  // Phone, // TODO: Add phone call functionality
  Video
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { messageService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface MessagesProps {
  language: 'ar' | 'en'
}

interface Message {
  id: number
  subject: string
  subjectAr: string
  content: string
  contentAr: string
  sender: string
  recipient: string
  messageType: 'direct' | 'group' | 'announcement'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  status: 'sent' | 'delivered' | 'read' | 'archived'
  sentDate: string
  readDate?: string
  isStarred: boolean
  attachments?: string[]
}

const translations = {
  ar: {
    messages: 'الرسائل',
    addMessage: 'إضافة رسالة',
    editMessage: 'تعديل الرسالة',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذه الرسالة؟',
    searchPlaceholder: 'البحث في الرسائل...',
    subject: 'الموضوع',
    content: 'المحتوى',
    sender: 'المرسل',
    recipient: 'المستقبل',
    messageType: 'نوع الرسالة',
    priority: 'الأولوية',
    status: 'الحالة',
    sentDate: 'تاريخ الإرسال',
    readDate: 'تاريخ القراءة',
    isStarred: 'مميز',
    attachments: 'المرفقات',
    sent: 'مرسل',
    delivered: 'تم التسليم',
    read: 'مقروء',
    archived: 'مؤرشف',
    direct: 'مباشر',
    group: 'جماعي',
    announcement: 'إعلان',
    high: 'عالي',
    medium: 'متوسط',
    low: 'منخفض',
    urgent: 'عاجل'
  },
  en: {
    messages: 'Messages',
    addMessage: 'Add Message',
    editMessage: 'Edit Message',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this message?',
    searchPlaceholder: 'Search messages...',
    subject: 'Subject',
    content: 'Content',
    sender: 'Sender',
    recipient: 'Recipient',
    messageType: 'Message Type',
    priority: 'Priority',
    status: 'Status',
    sentDate: 'Sent Date',
    readDate: 'Read Date',
    isStarred: 'Starred',
    attachments: 'Attachments',
    sent: 'Sent',
    delivered: 'Delivered',
    read: 'Read',
    archived: 'Archived',
    direct: 'Direct',
    group: 'Group',
    announcement: 'Announcement',
    high: 'High',
    medium: 'Medium',
    low: 'Low',
    urgent: 'Urgent'
  }
}

export default function Messages({ language }: MessagesProps): React.ReactElement {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook with data invalidation
  const {
    items: messages,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<Message>({
    service: messageService,
    autoLoad: true,
    pageSize: 20,
    entityType: 'message', // FIXED: Added entity type for data invalidation
    enableInvalidation: true
  })

  // Helper functions
  const getStatusIcon = (status: string): void => {
    switch (status) {
      case 'sent':
        return <Send className="h-4 w-4 text-blue-400" />
      case 'delivered':
        return <CheckCheck className="h-4 w-4 text-green-400" />
      case 'read':
        return <Eye className="h-4 w-4 text-purple-400" />
      case 'archived':
        return <Archive className="h-4 w-4 text-gray-400" />
      default:
        return <Clock className="h-4 w-4 text-gray-400" />
    }
  }

  const getMessageTypeIcon = (type: string): void => {
    switch (type) {
      case 'direct':
        return <User className="h-4 w-4 text-blue-400" />
      case 'group':
        return <Users className="h-4 w-4 text-green-400" />
      case 'announcement':
        return <MessageSquare className="h-4 w-4 text-purple-400" />
      default:
        return <MessageSquare className="h-4 w-4 text-gray-400" />
    }
  }

  const getPriorityColor = (priority: string): void => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'medium':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'low':
        return 'bg-green-100 text-green-800 border-green-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  // Table columns configuration
  const columns: TableColumn<Message>[] = [
    {
      key: 'subject',
      label: t.subject,
      sortable: true,
      render: (item: Message) => (
        <div className="flex items-center gap-2">
          {getMessageTypeIcon(item.messageType)}
          <div>
            <div className="font-medium text-white">
              {language === 'ar' ? item.subjectAr : item.subject}
            </div>
            <div className="text-sm text-white/60">
              {language === 'ar' ? item.contentAr?.substring(0, 50) + '...' : item.content?.substring(0, 50) + '...'}
            </div>
          </div>
          {item.isStarred && <Star className="h-4 w-4 text-yellow-400 fill-current" />}
        </div>
      )
    },
    {
      key: 'sender',
      label: t.sender,
      sortable: true,
      render: (item: Message) => (
        <div className="flex items-center gap-1">
          <User className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">{item.sender}</span>
        </div>
      )
    },
    {
      key: 'recipient',
      label: t.recipient,
      render: (item: Message) => (
        <div className="flex items-center gap-1">
          <User className="h-3 w-3 text-green-400" />
          <span className="text-white/80">{item.recipient}</span>
        </div>
      )
    },
    {
      key: 'messageType',
      label: t.messageType,
      sortable: true,
      render: (item: Message) => (
        <Badge variant="outline" className="text-white border-white/20">
          {t[item.messageType as keyof typeof t]}
        </Badge>
      )
    },
    {
      key: 'priority',
      label: t.priority,
      sortable: true,
      render: (item: Message) => (
        <Badge className={getPriorityColor(item.priority)}>
          {t[item.priority as keyof typeof t]}
        </Badge>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: Message) => (
        <div className="flex items-center gap-1">
          {getStatusIcon(item.status)}
          <span className="text-white/80">{t[item.status as keyof typeof t]}</span>
        </div>
      )
    },
    {
      key: 'sentDate',
      label: t.sentDate,
      sortable: true,
      render: (item: Message) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">{item.sentDate}</span>
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction<Message>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: Message) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: Message) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: Message) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'messageType',
      label: t.messageType,
      options: [
        { label: t.direct, value: 'direct' },
        { label: t.group, value: 'group' },
        { label: t.announcement, value: 'announcement' }
      ]
    },
    {
      key: 'priority',
      label: t.priority,
      options: [
        { label: t.urgent, value: 'urgent' },
        { label: t.high, value: 'high' },
        { label: t.medium, value: 'medium' },
        { label: t.low, value: 'low' }
      ]
    },
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.sent, value: 'sent' },
        { label: t.delivered, value: 'delivered' },
        { label: t.read, value: 'read' },
        { label: t.archived, value: 'archived' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'subject',
      label: t.subject,
      type: 'text',
      required: true
    },
    {
      name: 'subjectAr',
      label: t.subject + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'content',
      label: t.content,
      type: 'textarea',
      required: true
    },
    {
      name: 'contentAr',
      label: t.content + ' (عربي)',
      type: 'textarea',
      required: true
    },
    {
      name: 'recipient',
      label: t.recipient,
      type: 'text',
      required: true
    },
    {
      name: 'messageType',
      label: t.messageType,
      type: 'select',
      required: true,
      options: [
        { label: t.direct, value: 'direct' },
        { label: t.group, value: 'group' },
        { label: t.announcement, value: 'announcement' }
      ]
    },
    {
      name: 'priority',
      label: t.priority,
      type: 'select',
      required: true,
      options: [
        { label: t.urgent, value: 'urgent' },
        { label: t.high, value: 'high' },
        { label: t.medium, value: 'medium' },
        { label: t.low, value: 'low' }
      ]
    },
    {
      name: 'isStarred',
      label: t.isStarred,
      type: 'checkbox'
    }
  ]

  // Event handlers
  const handleCreate = (): void => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = (): void => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<Message>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('csv')
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.messages}
        data={messages}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addMessage : modalMode === 'edit' ? t.editMessage : t.view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
