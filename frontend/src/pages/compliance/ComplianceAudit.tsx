/**
 * Compliance Audit Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  Shield,
  Eye,
  Edit,
  Trash2,
  Al<PERSON><PERSON>riangle,
  CheckCircle,
  Clock,
  FileText,
  Calendar,
  Target,
  User
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { complianceAuditService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface ComplianceAuditProps {
  language: 'ar' | 'en'
}

interface ComplianceItem {
  id: number
  title: string
  titleAr: string
  description: string
  descriptionAr: string
  category: string
  categoryAr: string
  regulation: string
  regulationAr: string
  status: 'compliant' | 'non-compliant' | 'pending' | 'in-review' | 'expired'
  priority: 'low' | 'medium' | 'high' | 'critical'
  lastAuditDate: string
  nextAuditDate: string
  auditor: string
  auditorAr: string
  department: string
  departmentAr: string
  findings: string
  findingsAr: string
  actionRequired: string
  actionRequiredAr: string
  dueDate: string
  assignedTo: string
  assignedToAr: string
  evidenceFiles: string
  riskLevel: 'low' | 'medium' | 'high' | 'critical'
  complianceScore: number
}

const translations = {
  ar: {
    complianceAudit: 'الامتثال والتدقيق',
    addCompliance: 'إضافة عنصر امتثال',
    editCompliance: 'تعديل عنصر الامتثال',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف عنصر الامتثال هذا؟',
    searchPlaceholder: 'البحث في عناصر الامتثال...',
    title: 'العنوان',
    description: 'الوصف',
    category: 'الفئة',
    regulation: 'اللائحة',
    status: 'الحالة',
    priority: 'الأولوية',
    lastAuditDate: 'تاريخ آخر تدقيق',
    nextAuditDate: 'تاريخ التدقيق القادم',
    auditor: 'المدقق',
    department: 'القسم',
    findings: 'النتائج',
    actionRequired: 'الإجراء المطلوب',
    dueDate: 'تاريخ الاستحقاق',
    assignedTo: 'مُكلف إلى',
    evidenceFiles: 'ملفات الأدلة',
    riskLevel: 'مستوى المخاطر',
    complianceScore: 'نقاط الامتثال',
    compliant: 'متوافق',
    'non-compliant': 'غير متوافق',
    pending: 'معلق',
    'in-review': 'قيد المراجعة',
    expired: 'منتهي',
    low: 'منخفض',
    medium: 'متوسط',
    high: 'عالي',
    critical: 'حرج',
    categories: {
      dataSecurity: 'أمان البيانات',
      financial: 'مالية',
      healthSafety: 'الصحة والسلامة',
      financialCrime: 'الجرائم المالية',
      legal: 'قانونية',
      operational: 'تشغيلية'
    }
  },
  en: {
    complianceAudit: 'Compliance & Audit',
    addCompliance: 'Add Compliance Item',
    editCompliance: 'Edit Compliance Item',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this compliance item?',
    searchPlaceholder: 'Search compliance items...',
    title: 'Title',
    description: 'Description',
    category: 'Category',
    regulation: 'Regulation',
    status: 'Status',
    priority: 'Priority',
    lastAuditDate: 'Last Audit Date',
    nextAuditDate: 'Next Audit Date',
    auditor: 'Auditor',
    department: 'Department',
    findings: 'Findings',
    actionRequired: 'Action Required',
    dueDate: 'Due Date',
    assignedTo: 'Assigned To',
    evidenceFiles: 'Evidence Files',
    riskLevel: 'Risk Level',
    complianceScore: 'Compliance Score',
    compliant: 'Compliant',
    'non-compliant': 'Non-Compliant',
    pending: 'Pending',
    'in-review': 'In Review',
    expired: 'Expired',
    low: 'Low',
    medium: 'Medium',
    high: 'High',
    critical: 'Critical',
    categories: {
      dataSecurity: 'Data Security',
      financial: 'Financial',
      healthSafety: 'Health & Safety',
      financialCrime: 'Financial Crime',
      legal: 'Legal',
      operational: 'Operational'
    }
  }
}

export default function ComplianceAudit({ language }: ComplianceAuditProps): React.ReactElement {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: complianceItems,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<ComplianceItem>({
    service: complianceAuditService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getStatusColor = (status: string): void => {
    switch (status) {
      case 'compliant': return 'bg-green-100 text-green-800 border-green-200'
      case 'non-compliant': return 'bg-red-100 text-red-800 border-red-200'
      case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'in-review': return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'expired': return 'bg-gray-100 text-gray-800 border-gray-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getPriorityColor = (priority: string): void => {
    switch (priority) {
      case 'low': return 'text-green-400'
      case 'medium': return 'text-yellow-400'
      case 'high': return 'text-orange-400'
      case 'critical': return 'text-red-400'
      default: return 'text-gray-400'
    }
  }

  const getRiskColor = (risk: string): void => {
    switch (risk) {
      case 'low': return 'text-green-400'
      case 'medium': return 'text-yellow-400'
      case 'high': return 'text-orange-400'
      case 'critical': return 'text-red-400'
      default: return 'text-gray-400'
    }
  }

  const getStatusIcon = (status: string): void => {
    switch (status) {
      case 'compliant': return <CheckCircle className="h-4 w-4 text-green-400" />
      case 'non-compliant': return <AlertTriangle className="h-4 w-4 text-red-400" />
      case 'pending': return <Clock className="h-4 w-4 text-yellow-400" />
      case 'in-review': return <Eye className="h-4 w-4 text-blue-400" />
      case 'expired': return <Calendar className="h-4 w-4 text-gray-400" />
      default: return <Clock className="h-4 w-4 text-gray-400" />
    }
  }

  // Table columns configuration
  const columns: TableColumn<ComplianceItem>[] = [
    {
      key: 'title',
      label: t.title,
      sortable: true,
      render: (item: ComplianceItem) => (
        <div className="flex items-center gap-2">
          <Shield className="h-4 w-4 text-blue-400" />
          <div>
            <div className="font-medium text-white">
              {language === 'ar' ? item.titleAr : item.title}
            </div>
            <div className="text-sm text-white/60">
              {language === 'ar' ? item.regulationAr?.substring(0, 50) + '...' : item.regulation?.substring(0, 50) + '...'}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'category',
      label: t.category,
      render: (item: ComplianceItem) => (
        <span className="text-white/80">
          {language === 'ar' ? item.categoryAr : item.category}
        </span>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: ComplianceItem) => (
        <div className="flex items-center gap-2">
          {getStatusIcon(item.status)}
          <Badge className={getStatusColor(item.status)}>
            {String(t[item.status as keyof typeof t])}
          </Badge>
        </div>
      )
    },
    {
      key: 'priority',
      label: t.priority,
      sortable: true,
      render: (item: ComplianceItem) => (
        <span className={`font-medium ${getPriorityColor(item.priority)}`}>
          {String(t[item.priority as keyof typeof t])}
        </span>
      )
    },
    {
      key: 'complianceScore',
      label: t.complianceScore,
      sortable: true,
      render: (item: ComplianceItem) => (
        <div className="flex items-center gap-2">
          <span className="text-white font-medium">{item.complianceScore}%</span>
          <div className={`w-2 h-2 rounded-full ${getRiskColor(item.riskLevel)}`}></div>
        </div>
      )
    },
    {
      key: 'nextAuditDate',
      label: t.nextAuditDate,
      sortable: true,
      render: (item: ComplianceItem) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">{item.nextAuditDate}</span>
        </div>
      )
    },
    {
      key: 'auditor',
      label: t.auditor,
      render: (item: ComplianceItem) => (
        <div className="flex items-center gap-1">
          <User className="h-3 w-3 text-purple-400" />
          <span className="text-white/80">
            {language === 'ar' ? item.auditorAr : item.auditor}
          </span>
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction<ComplianceItem>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: ComplianceItem) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: ComplianceItem) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: ComplianceItem) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.compliant, value: 'compliant' },
        { label: t['non-compliant'], value: 'non-compliant' },
        { label: t.pending, value: 'pending' },
        { label: t['in-review'], value: 'in-review' },
        { label: t.expired, value: 'expired' }
      ]
    },
    {
      key: 'priority',
      label: t.priority,
      options: [
        { label: t.low, value: 'low' },
        { label: t.medium, value: 'medium' },
        { label: t.high, value: 'high' },
        { label: t.critical, value: 'critical' }
      ]
    },
    {
      key: 'riskLevel',
      label: t.riskLevel,
      options: [
        { label: t.low, value: 'low' },
        { label: t.medium, value: 'medium' },
        { label: t.high, value: 'high' },
        { label: t.critical, value: 'critical' }
      ]
    },
    {
      key: 'category',
      label: t.category,
      options: [
        { label: t.categories.dataSecurity, value: 'dataSecurity' },
        { label: t.categories.financial, value: 'financial' },
        { label: t.categories.healthSafety, value: 'healthSafety' },
        { label: t.categories.financialCrime, value: 'financialCrime' },
        { label: t.categories.legal, value: 'legal' },
        { label: t.categories.operational, value: 'operational' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'title',
      label: t.title,
      type: 'text',
      required: true
    },
    {
      name: 'titleAr',
      label: t.title + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'description',
      label: t.description,
      type: 'textarea',
      required: true
    },
    {
      name: 'descriptionAr',
      label: t.description + ' (عربي)',
      type: 'textarea',
      required: true
    },
    {
      name: 'category',
      label: t.category,
      type: 'text',
      required: true
    },
    {
      name: 'categoryAr',
      label: t.category + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'regulation',
      label: t.regulation,
      type: 'text',
      required: true
    },
    {
      name: 'regulationAr',
      label: t.regulation + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: t.compliant, value: 'compliant' },
        { label: t['non-compliant'], value: 'non-compliant' },
        { label: t.pending, value: 'pending' },
        { label: t['in-review'], value: 'in-review' },
        { label: t.expired, value: 'expired' }
      ]
    },
    {
      name: 'priority',
      label: t.priority,
      type: 'select',
      required: true,
      options: [
        { label: t.low, value: 'low' },
        { label: t.medium, value: 'medium' },
        { label: t.high, value: 'high' },
        { label: t.critical, value: 'critical' }
      ]
    },
    {
      name: 'riskLevel',
      label: t.riskLevel,
      type: 'select',
      required: true,
      options: [
        { label: t.low, value: 'low' },
        { label: t.medium, value: 'medium' },
        { label: t.high, value: 'high' },
        { label: t.critical, value: 'critical' }
      ]
    },
    {
      name: 'lastAuditDate',
      label: t.lastAuditDate,
      type: 'date',
      required: true
    },
    {
      name: 'nextAuditDate',
      label: t.nextAuditDate,
      type: 'date',
      required: true
    },
    {
      name: 'auditor',
      label: t.auditor,
      type: 'text',
      required: true
    },
    {
      name: 'auditorAr',
      label: t.auditor + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'department',
      label: t.department,
      type: 'text',
      required: true
    },
    {
      name: 'departmentAr',
      label: t.department + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'findings',
      label: t.findings,
      type: 'textarea'
    },
    {
      name: 'findingsAr',
      label: t.findings + ' (عربي)',
      type: 'textarea'
    },
    {
      name: 'actionRequired',
      label: t.actionRequired,
      type: 'textarea'
    },
    {
      name: 'actionRequiredAr',
      label: t.actionRequired + ' (عربي)',
      type: 'textarea'
    },
    {
      name: 'dueDate',
      label: t.dueDate,
      type: 'date'
    },
    {
      name: 'assignedTo',
      label: t.assignedTo,
      type: 'text'
    },
    {
      name: 'assignedToAr',
      label: t.assignedTo + ' (عربي)',
      type: 'text'
    },
    {
      name: 'evidenceFiles',
      label: t.evidenceFiles,
      type: 'text',
      placeholder: 'Comma-separated file names'
    },
    {
      name: 'complianceScore',
      label: t.complianceScore,
      type: 'number',
      required: true,
      min: 0,
      max: 100
    }
  ]

  // Event handlers
  const handleCreate = (): void => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = (): void => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<ComplianceItem>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('csv')
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.complianceAudit}
        data={complianceItems}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addCompliance : modalMode === 'edit' ? t.editCompliance : t.view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
