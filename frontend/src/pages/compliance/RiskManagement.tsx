/**
 * Risk Management Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState, ReactElement } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  AlertTriangle,
  Shield,
  Eye,
  Edit,
  Trash2,
  CheckCircle,
  XCircle,
  Activity,
  Calendar,
  User,
  Building
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { riskManagementService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface RiskManagementProps {
  language: 'ar' | 'en'
}

interface Risk {
  id: number
  title: string
  titleAr: string
  description: string
  descriptionAr: string
  category: string
  categoryAr: string
  probability: 'very-low' | 'low' | 'medium' | 'high' | 'very-high'
  impact: 'very-low' | 'low' | 'medium' | 'high' | 'very-high'
  riskLevel: 'low' | 'medium' | 'high' | 'critical'
  status: 'identified' | 'assessed' | 'mitigated' | 'monitored' | 'closed'
  owner: string
  ownerAr: string
  department: string
  departmentAr: string
  identifiedDate: string
  lastReviewDate: string
  nextReviewDate: string
  mitigationPlan: string
  mitigationPlanAr: string
  mitigationStatus: 'not-started' | 'in-progress' | 'completed' | 'overdue'
  residualRisk: 'low' | 'medium' | 'high' | 'critical'
  cost: number
  likelihood: number
  severity: number
  riskScore: number
}

const translations = {
  ar: {
    riskManagement: 'إدارة المخاطر',
    addRisk: 'إضافة مخاطرة',
    editRisk: 'تعديل المخاطرة',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذه المخاطرة؟',
    searchPlaceholder: 'البحث في المخاطر...',
    title: 'العنوان',
    description: 'الوصف',
    category: 'الفئة',
    probability: 'الاحتمالية',
    impact: 'التأثير',
    riskLevel: 'مستوى المخاطر',
    status: 'الحالة',
    owner: 'المسؤول',
    department: 'القسم',
    identifiedDate: 'تاريخ التحديد',
    lastReviewDate: 'تاريخ آخر مراجعة',
    nextReviewDate: 'تاريخ المراجعة القادمة',
    mitigationPlan: 'خطة التخفيف',
    mitigationStatus: 'حالة التخفيف',
    residualRisk: 'المخاطر المتبقية',
    cost: 'التكلفة',
    likelihood: 'الاحتمالية (1-5)',
    severity: 'الشدة (1-5)',
    riskScore: 'نتيجة المخاطر',
    'very-low': 'منخفض جداً',
    low: 'منخفض',
    medium: 'متوسط',
    high: 'عالي',
    'very-high': 'عالي جداً',
    critical: 'حرج',
    identified: 'محدد',
    assessed: 'مقيم',
    mitigated: 'مخفف',
    monitored: 'مراقب',
    closed: 'مغلق',
    'not-started': 'لم يبدأ',
    'in-progress': 'قيد التنفيذ',
    completed: 'مكتمل',
    overdue: 'متأخر'
  },
  en: {
    riskManagement: 'Risk Management',
    addRisk: 'Add Risk',
    editRisk: 'Edit Risk',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this risk?',
    searchPlaceholder: 'Search risks...',
    title: 'Title',
    description: 'Description',
    category: 'Category',
    probability: 'Probability',
    impact: 'Impact',
    riskLevel: 'Risk Level',
    status: 'Status',
    owner: 'Owner',
    department: 'Department',
    identifiedDate: 'Identified Date',
    lastReviewDate: 'Last Review Date',
    nextReviewDate: 'Next Review Date',
    mitigationPlan: 'Mitigation Plan',
    mitigationStatus: 'Mitigation Status',
    residualRisk: 'Residual Risk',
    cost: 'Cost',
    likelihood: 'Likelihood (1-5)',
    severity: 'Severity (1-5)',
    riskScore: 'Risk Score',
    'very-low': 'Very Low',
    low: 'Low',
    medium: 'Medium',
    high: 'High',
    'very-high': 'Very High',
    critical: 'Critical',
    identified: 'Identified',
    assessed: 'Assessed',
    mitigated: 'Mitigated',
    monitored: 'Monitored',
    closed: 'Closed',
    'not-started': 'Not Started',
    'in-progress': 'In Progress',
    completed: 'Completed',
    overdue: 'Overdue'
  }
}

export default function RiskManagement({ language }: RiskManagementProps): React.ReactElement {
  const [showModal, setShowModal] = useState<boolean>(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook with data invalidation
  const {
    items: risks,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<Risk>({
    service: riskManagementService,
    autoLoad: true,
    pageSize: 20,
    entityType: 'risk', // FIXED: Added entity type for data invalidation
    enableInvalidation: true
  })

  // Helper functions
  const getRiskLevelColor = (level: string): void => {
    switch (level) {
      case 'low': return 'bg-green-100 text-green-800 border-green-200'
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'critical': return 'bg-red-100 text-red-800 border-red-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusColor = (status: string): void => {
    switch (status) {
      case 'identified': return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'assessed': return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'mitigated': return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'monitored': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'closed': return 'bg-green-100 text-green-800 border-green-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getMitigationStatusColor = (status: string): void => {
    switch (status) {
      case 'not-started': return 'text-gray-400'
      case 'in-progress': return 'text-blue-400'
      case 'completed': return 'text-green-400'
      case 'overdue': return 'text-red-400'
      default: return 'text-gray-400'
    }
  }

  const getRiskIcon = (level: string): void => {
    switch (level) {
      case 'low': return <Shield className="w-4 h-4 text-green-400" />
      case 'medium': return <Activity className="w-4 h-4 text-yellow-400" />
      case 'high': return <AlertTriangle className="w-4 h-4 text-orange-400" />
      case 'critical': return <XCircle className="w-4 h-4 text-red-400" />
      default: return <Shield className="w-4 h-4 text-gray-400" />
    }
  }

  const getStatusIcon = (status: string): void => {
    switch (status) {
      case 'identified': return <AlertTriangle className="w-4 h-4" />
      case 'assessed': return <Activity className="w-4 h-4" />
      case 'mitigated': return <Shield className="w-4 h-4" />
      case 'monitored': return <Eye className="w-4 h-4" />
      case 'closed': return <CheckCircle className="w-4 h-4" />
      default: return <AlertTriangle className="w-4 h-4" />
    }
  }

  // Table columns configuration
  const columns: TableColumn<Risk>[] = [
    {
      key: 'title',
      label: t.title,
      sortable: true,
      render: (item: Risk) => (
        <div className="flex items-center gap-2">
          {getRiskIcon(item.riskLevel)}
          <div>
            <div className="font-medium text-white">
              {language === 'ar' ? item.titleAr : item.title}
            </div>
            <div className="text-sm text-white/60">
              {language === 'ar' ? item.categoryAr : item.category}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'riskLevel',
      label: t.riskLevel,
      sortable: true,
      render: (item: Risk) => (
        <div className="flex items-center gap-2">
          {getRiskIcon(item.riskLevel)}
          <Badge className={getRiskLevelColor(item.riskLevel)}>
            {t[item.riskLevel as keyof typeof t]}
          </Badge>
        </div>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: Risk) => (
        <div className="flex items-center gap-2">
          {getStatusIcon(item.status)}
          <Badge className={getStatusColor(item.status)}>
            {t[item.status as keyof typeof t]}
          </Badge>
        </div>
      )
    },
    {
      key: 'riskScore',
      label: t.riskScore,
      sortable: true,
      render: (item: Risk) => (
        <div className="flex items-center gap-2">
          <span className={`font-medium ${
            item.riskScore >= 15 ? 'text-red-400' :
            item.riskScore >= 10 ? 'text-orange-400' :
            item.riskScore >= 5 ? 'text-yellow-400' : 'text-green-400'
          }`}>
            {item.riskScore}
          </span>
          <span className="text-white/60 text-sm">
            ({item.likelihood}×{item.severity})
          </span>
        </div>
      )
    },
    {
      key: 'owner',
      label: t.owner,
      render: (item: Risk) => (
        <div className="flex items-center gap-1">
          <User className="h-3 w-3 text-purple-400" />
          <span className="text-white/80">
            {language === 'ar' ? item.ownerAr : item.owner}
          </span>
        </div>
      )
    },
    {
      key: 'department',
      label: t.department,
      render: (item: Risk) => (
        <div className="flex items-center gap-1">
          <Building className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">
            {language === 'ar' ? item.departmentAr : item.department}
          </span>
        </div>
      )
    },
    {
      key: 'nextReviewDate',
      label: t.nextReviewDate,
      sortable: true,
      render: (item: Risk) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">{item.nextReviewDate}</span>
        </div>
      )
    },
    {
      key: 'mitigationStatus',
      label: t.mitigationStatus,
      render: (item: Risk) => (
        <span className={`font-medium ${getMitigationStatusColor(item.mitigationStatus)}`}>
          {t[item.mitigationStatus as keyof typeof t]}
        </span>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction<Risk>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: Risk) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: Risk) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: Risk) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.identified, value: 'identified' },
        { label: t.assessed, value: 'assessed' },
        { label: t.mitigated, value: 'mitigated' },
        { label: t.monitored, value: 'monitored' },
        { label: t.closed, value: 'closed' }
      ]
    },
    {
      key: 'riskLevel',
      label: t.riskLevel,
      options: [
        { label: t.low, value: 'low' },
        { label: t.medium, value: 'medium' },
        { label: t.high, value: 'high' },
        { label: t.critical, value: 'critical' }
      ]
    },
    {
      key: 'probability',
      label: t.probability,
      options: [
        { label: t['very-low'], value: 'very-low' },
        { label: t.low, value: 'low' },
        { label: t.medium, value: 'medium' },
        { label: t.high, value: 'high' },
        { label: t['very-high'], value: 'very-high' }
      ]
    },
    {
      key: 'impact',
      label: t.impact,
      options: [
        { label: t['very-low'], value: 'very-low' },
        { label: t.low, value: 'low' },
        { label: t.medium, value: 'medium' },
        { label: t.high, value: 'high' },
        { label: t['very-high'], value: 'very-high' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'title',
      label: t.title,
      type: 'text',
      required: true
    },
    {
      name: 'titleAr',
      label: t.title + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'description',
      label: t.description,
      type: 'textarea',
      required: true
    },
    {
      name: 'descriptionAr',
      label: t.description + ' (عربي)',
      type: 'textarea',
      required: true
    },
    {
      name: 'category',
      label: t.category,
      type: 'text',
      required: true
    },
    {
      name: 'categoryAr',
      label: t.category + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'probability',
      label: t.probability,
      type: 'select',
      required: true,
      options: [
        { label: t['very-low'], value: 'very-low' },
        { label: t.low, value: 'low' },
        { label: t.medium, value: 'medium' },
        { label: t.high, value: 'high' },
        { label: t['very-high'], value: 'very-high' }
      ]
    },
    {
      name: 'impact',
      label: t.impact,
      type: 'select',
      required: true,
      options: [
        { label: t['very-low'], value: 'very-low' },
        { label: t.low, value: 'low' },
        { label: t.medium, value: 'medium' },
        { label: t.high, value: 'high' },
        { label: t['very-high'], value: 'very-high' }
      ]
    },
    {
      name: 'riskLevel',
      label: t.riskLevel,
      type: 'select',
      required: true,
      options: [
        { label: t.low, value: 'low' },
        { label: t.medium, value: 'medium' },
        { label: t.high, value: 'high' },
        { label: t.critical, value: 'critical' }
      ]
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: t.identified, value: 'identified' },
        { label: t.assessed, value: 'assessed' },
        { label: t.mitigated, value: 'mitigated' },
        { label: t.monitored, value: 'monitored' },
        { label: t.closed, value: 'closed' }
      ]
    },
    {
      name: 'owner',
      label: t.owner,
      type: 'text',
      required: true
    },
    {
      name: 'ownerAr',
      label: t.owner + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'department',
      label: t.department,
      type: 'text',
      required: true
    },
    {
      name: 'departmentAr',
      label: t.department + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'identifiedDate',
      label: t.identifiedDate,
      type: 'date',
      required: true
    },
    {
      name: 'lastReviewDate',
      label: t.lastReviewDate,
      type: 'date'
    },
    {
      name: 'nextReviewDate',
      label: t.nextReviewDate,
      type: 'date',
      required: true
    },
    {
      name: 'mitigationPlan',
      label: t.mitigationPlan,
      type: 'textarea'
    },
    {
      name: 'mitigationStatus',
      label: t.mitigationStatus,
      type: 'select',
      options: [
        { label: t['not-started'], value: 'not-started' },
        { label: t['in-progress'], value: 'in-progress' },
        { label: t.completed, value: 'completed' },
        { label: t.overdue, value: 'overdue' }
      ]
    },
    {
      name: 'residualRisk',
      label: t.residualRisk,
      type: 'select',
      options: [
        { label: t.low, value: 'low' },
        { label: t.medium, value: 'medium' },
        { label: t.high, value: 'high' },
        { label: t.critical, value: 'critical' }
      ]
    },
    {
      name: 'cost',
      label: t.cost,
      type: 'number',
      min: 0
    },
    {
      name: 'likelihood',
      label: t.likelihood,
      type: 'number',
      min: 1,
      max: 5,
      required: true
    },
    {
      name: 'severity',
      label: t.severity,
      type: 'number',
      min: 1,
      max: 5,
      required: true
    }
  ]

  // Event handlers
  const handleCreate = (): void => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = (): void => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<Risk>) => {
    try {
      // Calculate risk score from likelihood and severity
      if ((data as any).likelihood && (data as any).severity) {
        (data as any).riskScore = (data as any).likelihood * (data as any).severity
      }

      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('csv')
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.riskManagement}
        data={risks}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addRisk : modalMode === 'edit' ? t.editRisk : t.view}
        fields={formFields}
        initialData={selectedItem as unknown}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
