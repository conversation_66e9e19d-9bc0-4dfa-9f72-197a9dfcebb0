import React from 'react';
import { useState, useEffect } from 'react'
import { useSelector } from 'react-redux'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import type { RootState } from '../../store'
import {
  Users,
  Target,
  CheckSquare,
  Clock,
  TrendingUp,
  BarChart3,
  Calendar,
  Award,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Eye,
  Plus,
  FileText,
  Briefcase,
  User
} from 'lucide-react'

interface DepartmentManagerDashboardProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    welcome: 'مرحباً بك',
    departmentOverview: 'نظرة عامة على القسم',
    teamManagement: 'إدارة الفريق',
    projectTracking: 'تتبع المشاريع',
    performanceMetrics: 'مقاييس الأداء',
    taskManagement: 'إدارة المهام',
    quickActions: 'إجراءات سريعة',
    teamMembers: 'أعضاء الفريق',
    activeProjects: 'المشاريع النشطة',
    completedTasks: 'المهام المكتملة',
    pendingTasks: 'المهام المعلقة',
    teamPerformance: 'أداء الفريق',
    projectProgress: 'تقدم المشاريع',
    upcomingDeadlines: 'المواعيد النهائية القادمة',
    recentActivities: 'الأنشطة الحديثة',
    departmentGoals: 'أهداف القسم',
    assignTask: 'تعيين مهمة',
    createProject: 'إنشاء مشروع',
    teamMeeting: 'اجتماع الفريق',
    generateReport: 'إنشاء تقرير',
    refresh: 'تحديث',
    viewDetails: 'عرض التفاصيل',
    onTime: 'في الوقت المحدد',
    delayed: 'متأخر',
    completed: 'مكتمل',
    inProgress: 'قيد التنفيذ',
    high: 'عالي',
    medium: 'متوسط',
    low: 'منخفض',
    excellent: 'ممتاز',
    good: 'جيد',
    needsImprovement: 'يحتاج تحسين'
  },
  en: {
    welcome: 'Welcome',
    departmentOverview: 'Department Overview',
    teamManagement: 'Team Management',
    projectTracking: 'Project Tracking',
    performanceMetrics: 'Performance Metrics',
    taskManagement: 'Task Management',
    quickActions: 'Quick Actions',
    teamMembers: 'Team Members',
    activeProjects: 'Active Projects',
    completedTasks: 'Completed Tasks',
    pendingTasks: 'Pending Tasks',
    teamPerformance: 'Team Performance',
    projectProgress: 'Project Progress',
    upcomingDeadlines: 'Upcoming Deadlines',
    recentActivities: 'Recent Activities',
    departmentGoals: 'Department Goals',
    assignTask: 'Assign Task',
    createProject: 'Create Project',
    teamMeeting: 'Team Meeting',
    generateReport: 'Generate Report',
    refresh: 'Refresh',
    viewDetails: 'View Details',
    onTime: 'On Time',
    delayed: 'Delayed',
    completed: 'Completed',
    inProgress: 'In Progress',
    high: 'High',
    medium: 'Medium',
    low: 'Low',
    excellent: 'Excellent',
    good: 'Good',
    needsImprovement: 'Needs Improvement'
  }
}

export default function DepartmentManagerDashboard({ language }: DepartmentManagerDashboardProps): React.ReactElement {
  const { user } = useSelector((state: RootState) => state.auth)
  const [refreshing, setRefreshing] = useState(false)
  const t = translations[language]
  const isRTL = language === 'ar'

  // Department-specific metrics
  const [departmentMetrics, setDepartmentMetrics] = useState({
    teamMembers: 12,
    activeProjects: 8,
    completedTasks: 156,
    pendingTasks: 24,
    teamPerformance: 87.5,
    projectsOnTime: 6,
    projectsDelayed: 2,
    monthlyGoalProgress: 78
  })

  const handleRefresh = async () => {
    setRefreshing(true)
    await new Promise(resolve => setTimeout(resolve, 1000))
    setRefreshing(false)
  }

  const departmentCards = [
    {
      title: t.teamMembers,
      value: departmentMetrics.teamMembers.toString(),
      change: '+2',
      trend: 'up',
      icon: Users,
      color: 'from-blue-500 to-blue-600'
    },
    {
      title: t.activeProjects,
      value: departmentMetrics.activeProjects.toString(),
      change: '+1',
      trend: 'up',
      icon: Briefcase,
      color: 'from-green-500 to-green-600'
    },
    {
      title: t.completedTasks,
      value: departmentMetrics.completedTasks.toString(),
      change: '+18',
      trend: 'up',
      icon: CheckSquare,
      color: 'from-purple-500 to-purple-600'
    },
    {
      title: t.pendingTasks,
      value: departmentMetrics.pendingTasks.toString(),
      change: '-5',
      trend: 'down',
      icon: Clock,
      color: 'from-orange-500 to-orange-600'
    },
    {
      title: t.teamPerformance,
      value: `${departmentMetrics.teamPerformance}%`,
      change: '+3.2%',
      trend: 'up',
      icon: TrendingUp,
      color: 'from-cyan-500 to-cyan-600'
    },
    {
      title: t.projectProgress,
      value: `${departmentMetrics.monthlyGoalProgress}%`,
      change: t.onTime,
      trend: 'stable',
      icon: Target,
      color: 'from-emerald-500 to-emerald-600'
    }
  ]

  const quickActions = [
    { title: t.assignTask, icon: CheckSquare, href: '/projects/tasks', color: 'from-blue-500 to-blue-600' },
    { title: t.createProject, icon: Plus, href: '/projects', color: 'from-green-500 to-green-600' },
    { title: t.teamMeeting, icon: Calendar, href: '/communication/meetings', color: 'from-purple-500 to-purple-600' },
    { title: t.generateReport, icon: FileText, href: '/reports', color: 'from-orange-500 to-orange-600' }
  ]

  const upcomingDeadlines = [
    {
      id: 1,
      project: 'Website Redesign',
      projectAr: 'إعادة تصميم الموقع',
      task: 'UI/UX Review',
      taskAr: 'مراجعة واجهة المستخدم',
      assignee: 'سارة أحمد',
      deadline: '2024-01-25',
      priority: 'high',
      status: 'inProgress'
    },
    {
      id: 2,
      project: 'Mobile App',
      projectAr: 'تطبيق الجوال',
      task: 'Testing Phase',
      taskAr: 'مرحلة الاختبار',
      assignee: 'أحمد محمد',
      deadline: '2024-01-28',
      priority: 'medium',
      status: 'inProgress'
    },
    {
      id: 3,
      project: 'Database Migration',
      projectAr: 'نقل قاعدة البيانات',
      task: 'Data Backup',
      taskAr: 'نسخ احتياطي للبيانات',
      assignee: 'فاطمة علي',
      deadline: '2024-01-30',
      priority: 'high',
      status: 'pending'
    }
  ]

  const recentActivities = [
    {
      id: 1,
      type: 'task_completed',
      message: 'Sarah completed UI design for mobile app',
      messageAr: 'أكملت سارة تصميم واجهة المستخدم لتطبيق الجوال',
      timestamp: '2 hours ago',
      user: 'سارة أحمد',
      icon: CheckCircle,
      severity: 'success'
    },
    {
      id: 2,
      type: 'project_update',
      message: 'Website project milestone reached',
      messageAr: 'تم الوصول إلى معلم مشروع الموقع',
      timestamp: '4 hours ago',
      user: 'أحمد محمد',
      icon: Target,
      severity: 'info'
    },
    {
      id: 3,
      type: 'deadline_alert',
      message: 'Database migration deadline approaching',
      messageAr: 'اقتراب موعد نهاية نقل قاعدة البيانات',
      timestamp: '6 hours ago',
      user: 'النظام',
      icon: AlertTriangle,
      severity: 'warning'
    }
  ]

  const teamPerformance = [
    { name: 'سارة أحمد', nameEn: 'Sarah Ahmed', score: 95, status: 'excellent' },
    { name: 'أحمد محمد', nameEn: 'Ahmed Mohammed', score: 88, status: 'good' },
    { name: 'فاطمة علي', nameEn: 'Fatima Ali', score: 92, status: 'excellent' },
    { name: 'عمر سالم', nameEn: 'Omar Salem', score: 76, status: 'good' }
  ]

  const getStatusColor = (trend: string): void => {
    switch (trend) {
      case 'up':
        return 'text-green-400'
      case 'down':
        return 'text-red-400'
      default:
        return 'text-blue-400'
    }
  }

  const getPriorityColor = (priority: string): void => {
    switch (priority) {
      case 'high':
        return 'border-l-red-500 bg-red-500/10'
      case 'medium':
        return 'border-l-yellow-500 bg-yellow-500/10'
      case 'low':
        return 'border-l-green-500 bg-green-500/10'
      default:
        return 'border-l-blue-500 bg-blue-500/10'
    }
  }

  const getSeverityIcon = (severity: string): void => {
    switch (severity) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-400" />
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-400" />
      case 'error':
        return <AlertTriangle className="h-4 w-4 text-red-400" />
      default:
        return <Target className="h-4 w-4 text-blue-400" />
    }
  }

  const getPerformanceColor = (status: string): void => {
    switch (status) {
      case 'excellent':
        return 'text-green-400'
      case 'good':
        return 'text-blue-400'
      default:
        return 'text-yellow-400'
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">
            {t.welcome}, {user?.first_name} {user?.last_name}
          </h1>
          <p className="text-white/70">لوحة تحكم مدير القسم - إدارة الفريق والمشاريع</p>
        </div>
        
        <div className="flex items-center gap-3">
          <Button
            onClick={handleRefresh}
            disabled={refreshing}
            className="glass-button"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            {t.refresh}
          </Button>
          <Button variant="outline" className="glass-button">
            <FileText className="h-4 w-4 mr-2" />
            {t.generateReport}
          </Button>
        </div>
      </div>

      {/* Department Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
        {departmentCards.map((card, index) => (
          <Card key={index} className="glass-card border-white/20 hover:border-white/40 transition-all">
            <CardContent className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <p className="text-white/70 text-sm mb-1">{card.title}</p>
                  <p className="text-2xl font-bold text-white">{card.value}</p>
                </div>
                <div className={`p-3 rounded-xl bg-gradient-to-r ${card.color} shadow-lg`}>
                  <card.icon className="h-6 w-6 text-white" />
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <span className={`text-sm font-medium ${getStatusColor(card.trend)}`}>
                  {card.change}
                </span>
                <span className="text-xs text-white/60">vs last week</span>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Main Dashboard Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Quick Actions */}
        <Card className="glass-card border-white/20">
          <CardHeader>
            <CardTitle className="text-white text-xl">{t.quickActions}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              {quickActions.map((action, index) => (
                <div
                  key={index}
                  className="group flex flex-col items-center gap-3 p-6 glass-card border-white/10 hover:border-white/30 cursor-pointer transition-all duration-300 hover:scale-105"
                >
                  <div className={`p-4 rounded-2xl bg-gradient-to-r ${action.color} shadow-lg group-hover:shadow-xl transition-all`}>
                    <action.icon className="h-8 w-8 text-white" />
                  </div>
                  <span className="font-medium text-white text-center text-sm">
                    {action.title}
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Upcoming Deadlines */}
        <Card className="lg:col-span-2 glass-card border-white/20">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-white text-xl">{t.upcomingDeadlines}</CardTitle>
              <Button variant="outline" size="sm" className="glass-button">
                <Eye className="h-4 w-4 mr-2" />
                {t.viewDetails}
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {upcomingDeadlines.map((deadline) => (
                <div key={deadline.id} className={`p-4 glass-card border-white/10 hover:border-white/30 transition-all border-l-4 ${getPriorityColor(deadline.priority)}`}>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h4 className="text-white font-medium">
                          {language === 'ar' ? deadline.taskAr : deadline.task}
                        </h4>
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          deadline.priority === 'high' ? 'bg-red-500/20 text-red-400' :
                          deadline.priority === 'medium' ? 'bg-yellow-500/20 text-yellow-400' :
                          'bg-green-500/20 text-green-400'
                        }`}>
                          {t[deadline.priority as keyof typeof t]}
                        </span>
                      </div>
                      <div className="grid grid-cols-2 gap-4 text-sm text-white/80">
                        <div>
                          <span className="text-white/60">المشروع: </span>
                          {language === 'ar' ? deadline.projectAr : deadline.project}
                        </div>
                        <div>
                          <span className="text-white/60">المسؤول: </span>
                          {deadline.assignee}
                        </div>
                        <div>
                          <span className="text-white/60">الموعد النهائي: </span>
                          {deadline.deadline}
                        </div>
                        <div>
                          <span className="text-white/60">الحالة: </span>
                          {t[deadline.status as keyof typeof t]}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Team Performance & Recent Activities */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="glass-card border-white/20">
          <CardHeader>
            <CardTitle className="text-white text-xl flex items-center gap-2">
              <Award className="h-5 w-5" />
              {t.teamPerformance}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {teamPerformance.map((member, index) => (
                <div key={index} className="flex items-center justify-between p-3 glass-card border-white/10 hover:border-white/30 transition-all">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-white font-bold">
                      {(language === 'ar' ? member.name : member.nameEn).charAt(0)}
                    </div>
                    <div>
                      <p className="text-white font-medium">
                        {language === 'ar' ? member.name : member.nameEn}
                      </p>
                      <p className={`text-sm ${getPerformanceColor(member.status)}`}>
                        {t[member.status as keyof typeof t]}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-white font-bold text-lg">{member.score}%</p>
                    <div className="w-16 bg-white/20 rounded-full h-2 mt-1">
                      <div 
                        className={`h-2 rounded-full ${getPerformanceColor(member.status).replace('text-', 'bg-')}`}
                        style={{ width: `${member.score}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardHeader>
            <CardTitle className="text-white text-xl">{t.recentActivities}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivities.map((activity) => (
                <div key={activity.id} className="flex items-start gap-3 p-4 glass-card border-white/10 hover:border-white/30 transition-all">
                  <div className="p-2 rounded-lg bg-white/10">
                    {getSeverityIcon(activity.severity)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-white text-sm font-medium mb-1">
                      {language === 'ar' ? activity.messageAr : activity.message}
                    </p>
                    <div className="flex items-center gap-2 text-xs text-white/60">
                      <User className="h-3 w-3" />
                      <span>{activity.user}</span>
                      <Clock className="h-3 w-3 ml-2" />
                      <span>{activity.timestamp}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
