import React from 'react';
import { useState, useEffect } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import { useNavigate } from 'react-router-dom'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import type { RootState, AppDispatch } from '../../store'
import { taskAPI } from '@/services/projectAPI'
import { attendanceAPI, leaveRequestAPI, leaveTypeAPI } from '@/services/hrAPI'
import { employeeAPI } from '@/services/employeeAPI'
import { apiClient } from '@/services/api'
import {
  User,
  Calendar,
  CheckSquare,
  Clock,
  Award,
  MessageSquare,
  FileText,
  TrendingUp,
  Target,
  Coffee,
  MapPin,
  Phone,
  Mail,
  RefreshCw,
  Plus,
  Eye
} from 'lucide-react'

interface EmployeeDashboardProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    welcome: 'مرحباً بك',
    myProfile: 'ملفي الشخصي',
    myTasks: 'مهامي',
    mySchedule: 'جدولي',
    myAttendance: 'حضوري',
    leaveBalance: 'رصيد الإجازات',
    teamUpdates: 'تحديثات الفريق',
    quickActions: 'إجراءات سريعة',
    personalInfo: 'المعلومات الشخصية',
    workInfo: 'معلومات العمل',
    todayTasks: 'مهام اليوم',
    upcomingMeetings: 'الاجتماعات القادمة',
    recentMessages: 'الرسائل الحديثة',
    performanceOverview: 'نظرة عامة على الأداء',
    attendanceRecord: 'سجل الحضور',
    leaveRequests: 'طلبات الإجازة',
    tasksCompleted: 'المهام المكتملة',
    tasksInProgress: 'المهام قيد التنفيذ',
    tasksPending: 'المهام المعلقة',
    tasksOverdue: 'المهام المتأخرة',
    presentDays: 'أيام الحضور',
    absentDays: 'أيام الغياب',
    lateDays: 'أيام التأخير',
    overtimeHours: 'ساعات العمل الإضافي',
    annualLeave: 'الإجازة السنوية',
    sickLeave: 'الإجازة المرضية',
    emergencyLeave: 'الإجازة الطارئة',
    available: 'متاح',
    used: 'مستخدم',
    pending: 'معلق',
    requestLeave: 'طلب إجازة',
    viewProfile: 'عرض الملف الشخصي',
    updateInfo: 'تحديث المعلومات',
    viewTasks: 'عرض المهام',
    sendMessage: 'إرسال رسالة',
    viewCalendar: 'عرض التقويم',
    checkAttendance: 'تسجيل الحضور',
    refresh: 'تحديث',
    viewAll: 'عرض الكل',
    high: 'عالي',
    medium: 'متوسط',
    low: 'منخفض',
    completed: 'مكتمل',
    inProgress: 'قيد التنفيذ',
    overdue: 'متأخر',
    today: 'اليوم',
    tomorrow: 'غداً',
    thisWeek: 'هذا الأسبوع',
    noTasksToday: 'لا توجد مهام لليوم',
    viewAllTasks: 'عرض جميع المهام',
    noUpcomingMeetings: 'لا توجد اجتماعات قادمة',
    viewCalendarButton: 'عرض التقويم',
    performanceScore: 'نقاط الأداء'
  },
  en: {
    welcome: 'Welcome',
    myProfile: 'My Profile',
    myTasks: 'My Tasks',
    mySchedule: 'My Schedule',
    myAttendance: 'My Attendance',
    leaveBalance: 'Leave Balance',
    teamUpdates: 'Team Updates',
    quickActions: 'Quick Actions',
    personalInfo: 'Personal Information',
    workInfo: 'Work Information',
    todayTasks: "Today's Tasks",
    upcomingMeetings: 'Upcoming Meetings',
    recentMessages: 'Recent Messages',
    performanceOverview: 'Performance Overview',
    attendanceRecord: 'Attendance Record',
    leaveRequests: 'Leave Requests',
    tasksCompleted: 'Tasks Completed',
    tasksInProgress: 'Tasks In Progress',
    tasksPending: 'Tasks Pending',
    tasksOverdue: 'Tasks Overdue',
    presentDays: 'Present Days',
    absentDays: 'Absent Days',
    lateDays: 'Late Days',
    overtimeHours: 'Overtime Hours',
    annualLeave: 'Annual Leave',
    sickLeave: 'Sick Leave',
    emergencyLeave: 'Emergency Leave',
    available: 'Available',
    used: 'Used',
    pending: 'Pending',
    requestLeave: 'Request Leave',
    viewProfile: 'View Profile',
    updateInfo: 'Update Info',
    viewTasks: 'View Tasks',
    sendMessage: 'Send Message',
    viewCalendar: 'View Calendar',
    checkAttendance: 'Check Attendance',
    refresh: 'Refresh',
    viewAll: 'View All',
    high: 'High',
    medium: 'Medium',
    low: 'Low',
    completed: 'Completed',
    inProgress: 'In Progress',
    overdue: 'Overdue',
    today: 'Today',
    noTasksToday: 'No tasks for today',
    viewAllTasks: 'View All Tasks',
    noUpcomingMeetings: 'No upcoming meetings',
    viewCalendarButton: 'View Calendar',
    performanceScore: 'Performance Score',
    tomorrow: 'Tomorrow',
    thisWeek: 'This Week'
  }
}

export default function EmployeeDashboard({ language }: EmployeeDashboardProps): React.ReactElement {
  const { user } = useSelector((state: RootState) => state.auth)
  const navigate = useNavigate()
  const [refreshing, setRefreshing] = useState(false)
  const t = translations[language]
  const isRTL = language === 'ar'

  // Employee-specific data - now loaded from API
  const [employeeData, setEmployeeData] = useState({
    tasks: {
      completed: 0,
      inProgress: 0,
      pending: 0,
      overdue: 0
    },
    attendance: {
      presentDays: 0,
      absentDays: 0,
      lateDays: 0,
      overtimeHours: 0
    },
    leave: {
      annual: { available: 0, used: 0, pending: 0 },
      sick: { available: 0, used: 0, pending: 0 },
      emergency: { available: 0, used: 0, pending: 0 }
    },
    performance: {
      score: 0,
      goalsCompleted: 0,
      totalGoals: 0,
      feedback: ''
    }
  })

  // Loading states
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch real employee data from APIs
  const fetchEmployeeData = async () => {
    try {
      setLoading(true)
      setLoadingTasks(true)
      setLoadingMeetings(true)
      setLoadingMessages(true)
      setError(null)

      // Get current user info
      const currentUser = user

      if (!currentUser?.id) {
        throw new Error('User not authenticated')
      }

      // Fetch data in parallel
      const [tasksData, attendanceData, leaveRequestsData, leaveTypesData, meetingsData, messagesData] = await Promise.allSettled([
        taskAPI.getMyTasks(),
        attendanceAPI.getMyAttendance({
          date_from: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],
          date_to: new Date().toISOString().split('T')[0]
        }),
        leaveRequestAPI.getMyRequests(),
        leaveTypeAPI.getAll(),
        // FIXED: Use API client for proper authentication and base URL
        apiClient.get('/meetings/my-meetings/').then(res => res.data || []).catch(() => []),
        apiClient.get('/messages/recent/').then(res => res.data || []).catch(() => [])
      ])

      // Process tasks data
      let taskStats = { completed: 0, inProgress: 0, pending: 0, overdue: 0 }
      if (tasksData.status === 'fulfilled') {
        const tasksResponse = tasksData.value
        const tasks = Array.isArray(tasksResponse) ? tasksResponse : tasksResponse.results || []
        const now = new Date()

        taskStats = {
          completed: tasks.filter(t => t.status === 'COMPLETED').length,
          inProgress: tasks.filter(t => t.status === 'IN_PROGRESS').length,
          pending: tasks.filter(t => t.status === 'TODO').length,
          overdue: tasks.filter(t => t.status !== 'COMPLETED' && new Date(t.due_date) < now).length
        }
      }

      // Process attendance data
      let attendanceStats = { presentDays: 0, absentDays: 0, lateDays: 0, overtimeHours: 0 }
      if (attendanceData.status === 'fulfilled') {
        const attendanceResponse = attendanceData.value
        const attendance = Array.isArray(attendanceResponse) ? attendanceResponse : attendanceResponse.results || []
        attendanceStats = {
          presentDays: attendance.filter(a => a.is_present).length,
          absentDays: attendance.filter(a => !a.is_present).length,
          lateDays: attendance.filter(a => a.is_late).length,
          overtimeHours: attendance.reduce((sum, a) => sum + (a.overtime_hours || 0), 0)
        }
      }

      // Process leave data
      let leaveStats = {
        annual: { available: 0, used: 0, pending: 0 },
        sick: { available: 0, used: 0, pending: 0 },
        emergency: { available: 0, used: 0, pending: 0 }
      }

      if (leaveRequestsData.status === 'fulfilled' && leaveTypesData.status === 'fulfilled') {
        const leaveRequestsResponse = leaveRequestsData.value
        const leaveRequests = Array.isArray(leaveRequestsResponse) ? leaveRequestsResponse : leaveRequestsResponse.results || []
        const leaveTypesResponse = leaveTypesData.value
        const leaveTypes = Array.isArray(leaveTypesResponse) ? leaveTypesResponse : leaveTypesResponse.results || []

        // Calculate leave balances by type
        leaveTypes.forEach(type => {
          const requests = leaveRequests.filter(req => req.leave_type === type.id)
          const used = requests.filter(req => req.status === 'APPROVED').reduce((sum, req) => sum + req.days_requested, 0)
          const pending = requests.filter(req => req.status === 'PENDING').reduce((sum, req) => sum + req.days_requested, 0)

          const typeName = type.name.toLowerCase()
          if (typeName.includes('annual') || typeName.includes('vacation')) {
            leaveStats.annual = { available: type.days_allowed, used, pending }
          } else if (typeName.includes('sick')) {
            leaveStats.sick = { available: type.days_allowed, used, pending }
          } else if (typeName.includes('emergency')) {
            leaveStats.emergency = { available: type.days_allowed, used, pending }
          }
        })
      }

      // Calculate performance (simplified - in real app this would come from performance API)
      const performanceScore = taskStats.completed > 0 ?
        Math.min(100, Math.round((taskStats.completed / (taskStats.completed + taskStats.pending + taskStats.overdue)) * 100)) : 0

      // FIXED: Process meetings and messages data
      const meetings = meetingsData.status === 'fulfilled' ? meetingsData.value : []
      const messages = messagesData.status === 'fulfilled' ? messagesData.value : []

      // Update state with real data
      setEmployeeData({
        tasks: taskStats,
        attendance: attendanceStats,
        leave: leaveStats,
        performance: {
          score: performanceScore,
          goalsCompleted: taskStats.completed,
          totalGoals: taskStats.completed + taskStats.pending + taskStats.inProgress,
          feedback: performanceScore >= 80 ? 'Excellent performance' :
                   performanceScore >= 60 ? 'Good performance' : 'Needs improvement'
        }
      })

      // FIXED: Set real data for tasks, meetings, and messages
      const tasks = tasksData.status === 'fulfilled' ? tasksData.value : []
      setTodayTasks(Array.isArray(tasks) ? tasks.slice(0, 5) : []) // Show first 5 tasks
      setUpcomingMeetings(Array.isArray(meetings) ? meetings.slice(0, 3) : []) // Show first 3 meetings
      setRecentMessages(Array.isArray(messages) ? messages.slice(0, 5) : []) // Show first 5 messages

    } catch (err) {
      console.error('Error fetching employee data:', err)
      setError(err instanceof Error ? err.message : 'Failed to load employee data')
    } finally {
      setLoading(false)
      setLoadingTasks(false)
      setLoadingMeetings(false)
      setLoadingMessages(false)
    }
  }

  // Load data on component mount
  useEffect(() => {
    fetchEmployeeData()
  }, [user?.id])

  const handleRefresh = async () => {
    setRefreshing(true)
    await fetchEmployeeData()
    setRefreshing(false)
  }

  const quickActions = [
    {
      title: t.requestLeave,
      icon: Calendar,
      href: '/employee/leave',
      color: 'from-blue-500 to-blue-600',
      onClick: () => navigate('/employee/leave')
    },
    {
      title: t.viewTasks,
      icon: CheckSquare,
      href: '/employee/tasks',
      color: 'from-green-500 to-green-600',
      onClick: () => navigate('/employee/tasks')
    },
    {
      title: t.sendMessage,
      icon: MessageSquare,
      href: '/employee/messages',
      color: 'from-purple-500 to-purple-600',
      onClick: () => navigate('/employee/messages')
    },
    {
      title: t.updateInfo,
      icon: User,
      href: '/employee/profile',
      color: 'from-orange-500 to-orange-600',
      onClick: () => navigate('/employee/profile')
    }
  ]

  // FIXED: Remove hardcoded tasks - use real data from API
  const [todayTasks, setTodayTasks] = useState<any[]>([])
  const [loadingTasks, setLoadingTasks] = useState(true)

  // FIXED: Remove hardcoded meetings - use real data from API
  const [upcomingMeetings, setUpcomingMeetings] = useState<any[]>([])
  const [loadingMeetings, setLoadingMeetings] = useState(true)

  // FIXED: Remove hardcoded messages - use real data from API
  const [recentMessages, setRecentMessages] = useState<any[]>([])
  const [loadingMessages, setLoadingMessages] = useState(true)

  const getPriorityColor = (priority: string): void => {
    switch (priority) {
      case 'high':
        return 'border-l-red-500 bg-red-500/10'
      case 'medium':
        return 'border-l-yellow-500 bg-yellow-500/10'
      case 'low':
        return 'border-l-green-500 bg-green-500/10'
      default:
        return 'border-l-blue-500 bg-blue-500/10'
    }
  }

  const getStatusColor = (status: string): void => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'inProgress':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'overdue':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  // Show loading state
  if (loading) {
    return (
      <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <RefreshCw className="h-8 w-8 animate-spin text-white mx-auto mb-4" />
            <p className="text-white/70">Loading employee dashboard...</p>
          </div>
        </div>
      </div>
    )
  }

  // Show error state
  if (error) {
    return (
      <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="bg-red-500/20 border border-red-500/30 rounded-lg p-6 max-w-md">
              <h3 className="text-red-400 font-semibold mb-2">Error Loading Dashboard</h3>
              <p className="text-white/70 mb-4">{error}</p>
              <Button onClick={fetchEmployeeData} className="glass-button">
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">
            {t.welcome}, {user?.first_name} {user?.last_name}
          </h1>
          <p className="text-white/70">لوحة تحكم الموظف - إدارة مهامك وأنشطتك اليومية</p>
        </div>

        <div className="flex items-center gap-3">
          <Button
            onClick={handleRefresh}
            disabled={refreshing || loading}
            className="glass-button"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            {t.refresh}
          </Button>
          <Button
            variant="outline"
            className="glass-button"
            onClick={() => navigate('/employee/attendance')}
          >
            <Clock className="h-4 w-4 mr-2" />
            {t.checkAttendance}
          </Button>
        </div>
      </div>

      {/* Personal Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card
          className="glass-card border-white/20 cursor-pointer hover:border-white/40 transition-all duration-300 hover:scale-105"
          onClick={() => navigate('/employee/tasks')}
        >
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.tasksCompleted}</p>
                <p className="text-2xl font-bold text-green-400">{employeeData.tasks.completed}</p>
              </div>
              <CheckSquare className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>

        <Card
          className="glass-card border-white/20 cursor-pointer hover:border-white/40 transition-all duration-300 hover:scale-105"
          onClick={() => navigate('/employee/tasks')}
        >
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.tasksInProgress}</p>
                <p className="text-2xl font-bold text-blue-400">{employeeData.tasks.inProgress}</p>
              </div>
              <Clock className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>

        <Card
          className="glass-card border-white/20 cursor-pointer hover:border-white/40 transition-all duration-300 hover:scale-105"
          onClick={() => navigate('/employee/attendance')}
        >
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.presentDays}</p>
                <p className="text-2xl font-bold text-emerald-400">{employeeData.attendance.presentDays}</p>
              </div>
              <Calendar className="h-8 w-8 text-emerald-400" />
            </div>
          </CardContent>
        </Card>

        <Card
          className="glass-card border-white/20 cursor-pointer hover:border-white/40 transition-all duration-300 hover:scale-105"
          onClick={() => navigate('/employee/performance')}
        >
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm">{t.performanceScore}</p>
                <p className="text-2xl font-bold text-purple-400">{employeeData.performance.score}%</p>
              </div>
              <Award className="h-8 w-8 text-purple-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Dashboard Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Quick Actions */}
        <Card className="glass-card border-white/20">
          <CardHeader>
            <CardTitle className="text-white text-xl">{t.quickActions}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              {quickActions.map((action, index) => (
                <div
                  key={index}
                  onClick={action.onClick}
                  className="group flex flex-col items-center gap-3 p-6 glass-card border-white/10 hover:border-white/30 cursor-pointer transition-all duration-300 hover:scale-105"
                >
                  <div className={`p-4 rounded-2xl bg-gradient-to-r ${action.color} shadow-lg group-hover:shadow-xl transition-all`}>
                    <action.icon className="h-8 w-8 text-white" />
                  </div>
                  <span className="font-medium text-white text-center text-sm">
                    {action.title}
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Today's Tasks */}
        <Card className="lg:col-span-2 glass-card border-white/20">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-white text-xl">{t.todayTasks}</CardTitle>
              <Button
                variant="outline"
                size="sm"
                className="glass-button"
                onClick={() => navigate('/employee/tasks')}
              >
                <Eye className="h-4 w-4 mr-2" />
                {t.viewAll}
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {loadingTasks ? (
                <div className="flex items-center justify-center py-8">
                  <RefreshCw className="h-6 w-6 animate-spin text-white/60" />
                  <span className="ml-2 text-white/60">Loading tasks...</span>
                </div>
              ) : todayTasks.length === 0 ? (
                <div className="text-center py-8">
                  <CheckSquare className="h-12 w-12 text-white/40 mx-auto mb-3" />
                  <p className="text-white/60">{t.noTasksToday}</p>
                  <Button
                    size="sm"
                    className="glass-button mt-3"
                    onClick={() => navigate('/employee/tasks')}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    {t.viewAllTasks}
                  </Button>
                </div>
              ) : (
                todayTasks.map((task) => (
                  <div key={task.id} className={`p-4 glass-card border-white/10 hover:border-white/30 transition-all border-l-4 ${getPriorityColor(task.priority || 'medium')}`}>
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h4 className="text-white font-medium">
                            {task.title || task.name || 'Untitled Task'}
                          </h4>
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(task.status || 'pending')}`}>
                            {t[task.status as keyof typeof t] || task.status || 'Pending'}
                          </span>
                        </div>
                        <div className="flex items-center gap-4 text-sm text-white/80">
                          <div className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            <span>{task.due_date || task.dueTime || 'No due date'}</span>
                          </div>
                          {task.project && (
                            <div className="flex items-center gap-1">
                              <Target className="h-3 w-3" />
                              <span>{task.project}</span>
                            </div>
                          )}
                          <div className="flex items-center gap-1">
                            <Award className="h-3 w-3" />
                            <span className={`font-medium ${
                              task.priority === 'high' ? 'text-red-400' :
                              task.priority === 'medium' ? 'text-yellow-400' : 'text-green-400'
                            }`}>
                              {t[task.priority as keyof typeof t] || task.priority || 'Medium'}
                            </span>
                          </div>
                        </div>
                      </div>
                      <Button
                        size="sm"
                        className="glass-button"
                        onClick={() => navigate('/employee/tasks')}
                      >
                        <Eye className="h-3 w-3 mr-1" />
                        View
                      </Button>
                    </div>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Leave Balance & Upcoming Meetings */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="glass-card border-white/20">
          <CardHeader>
            <CardTitle className="text-white text-xl flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              {t.leaveBalance}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {Object.entries(employeeData.leave).map(([type, data]) => (
                <div key={type} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-white/80 font-medium">
                      {t[`${type}Leave` as keyof typeof t]}
                    </span>
                    <span className="text-white text-sm">
                      {data.available - data.used} / {data.available}
                    </span>
                  </div>
                  <div className="w-full bg-white/20 rounded-full h-2">
                    <div
                      className="bg-gradient-to-r from-blue-500 to-green-500 h-2 rounded-full"
                      style={{ width: `${((data.available - data.used) / data.available) * 100}%` }}
                    ></div>
                  </div>
                  <div className="flex justify-between text-xs text-white/60">
                    <span>{t.used}: {data.used}</span>
                    <span>{t.pending}: {data.pending}</span>
                  </div>
                </div>
              ))}
              <Button
                className="w-full glass-button mt-4"
                onClick={() => navigate('/employee/leave')}
              >
                <Plus className="h-4 w-4 mr-2" />
                {t.requestLeave}
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card className="glass-card border-white/20">
          <CardHeader>
            <CardTitle className="text-white text-xl flex items-center gap-2">
              <Clock className="h-5 w-5" />
              {t.upcomingMeetings}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {loadingMeetings ? (
                <div className="flex items-center justify-center py-8">
                  <RefreshCw className="h-6 w-6 animate-spin text-white/60" />
                  <span className="ml-2 text-white/60">Loading meetings...</span>
                </div>
              ) : upcomingMeetings.length === 0 ? (
                <div className="text-center py-8">
                  <Calendar className="h-12 w-12 text-white/40 mx-auto mb-3" />
                  <p className="text-white/60">{t.noUpcomingMeetings}</p>
                  <Button
                    size="sm"
                    className="glass-button mt-3"
                    onClick={() => navigate('/employee/calendar')}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    {t.viewCalendarButton}
                  </Button>
                </div>
              ) : (
                upcomingMeetings.map((meeting) => (
                  <div key={meeting.id} className="p-4 glass-card border-white/10 hover:border-white/30 transition-all">
                    <div className="flex items-start justify-between mb-3">
                      <h4 className="text-white font-medium">
                        {meeting.title || meeting.name || 'Untitled Meeting'}
                      </h4>
                      <span className="text-blue-400 text-sm font-medium">{meeting.time || meeting.start_time || 'No time set'}</span>
                    </div>
                    <div className="grid grid-cols-2 gap-4 text-sm text-white/80">
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        <span>{meeting.duration || '30 min'}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <User className="h-3 w-3" />
                        <span>{meeting.attendees || 0} attendees</span>
                      </div>
                      <div className="flex items-center gap-1 col-span-2">
                        <MapPin className="h-3 w-3" />
                        <span>{meeting.location || 'No location set'}</span>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
