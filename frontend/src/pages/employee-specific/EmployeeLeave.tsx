/**
 * Employee Leave Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  Calendar,
  Clock,
  Eye,
  Edit,
  Trash2,
  CheckCircle,
  AlertTriangle,
  CalendarDays,
  Plane,
  Heart,
  Briefcase,
  User,
  FileText
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { employeeLeaveService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface EmployeeLeaveProps {
  language: 'ar' | 'en'
}

interface EmployeeLeave {
  id: number
  type: 'annual' | 'sick' | 'emergency' | 'maternity' | 'paternity' | 'unpaid'
  typeAr: string
  startDate: string
  endDate: string
  duration: number
  reason: string
  reasonAr: string
  status: 'pending' | 'approved' | 'rejected'
  requestDate: string
  approvedBy?: string
  approvedByAr?: string
  comments?: string
  commentsAr?: string
  attachments?: string[]
}

const translations = {
  ar: {
    employeeLeave: 'طلبات الإجازة - الموظف',
    addLeave: 'إضافة طلب إجازة',
    editLeave: 'تعديل طلب الإجازة',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف طلب الإجازة؟',
    searchPlaceholder: 'البحث في طلبات الإجازة...',
    type: 'نوع الإجازة',
    startDate: 'تاريخ البداية',
    endDate: 'تاريخ النهاية',
    duration: 'المدة',
    reason: 'السبب',
    status: 'الحالة',
    requestDate: 'تاريخ الطلب',
    approvedBy: 'موافق من',
    comments: 'التعليقات',
    attachments: 'المرفقات',
    annual: 'إجازة سنوية',
    sick: 'إجازة مرضية',
    emergency: 'إجازة طارئة',
    maternity: 'إجازة أمومة',
    paternity: 'إجازة أبوة',
    unpaid: 'إجازة بدون راتب',
    pending: 'قيد المراجعة',
    approved: 'موافق عليه',
    rejected: 'مرفوض',
    days: 'أيام'
  },
  en: {
    employeeLeave: 'Leave Requests - Employee',
    addLeave: 'Add Leave Request',
    editLeave: 'Edit Leave Request',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this leave request?',
    searchPlaceholder: 'Search leave requests...',
    type: 'Leave Type',
    startDate: 'Start Date',
    endDate: 'End Date',
    duration: 'Duration',
    reason: 'Reason',
    status: 'Status',
    requestDate: 'Request Date',
    approvedBy: 'Approved By',
    comments: 'Comments',
    attachments: 'Attachments',
    annual: 'Annual Leave',
    sick: 'Sick Leave',
    emergency: 'Emergency Leave',
    maternity: 'Maternity Leave',
    paternity: 'Paternity Leave',
    unpaid: 'Unpaid Leave',
    pending: 'Pending',
    approved: 'Approved',
    rejected: 'Rejected',
    days: 'Days'
  }
}

export default function EmployeeLeave({ language }: EmployeeLeaveProps): React.ReactElement {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: leaveRequests,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<EmployeeLeave>({
    service: employeeLeaveService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getStatusColor = (status: string): void => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'rejected':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusIcon = (status: string): void => {
    switch (status) {
      case 'approved':
        return <CheckCircle className="h-3 w-3" />
      case 'pending':
        return <Clock className="h-3 w-3 animate-pulse" />
      case 'rejected':
        return <AlertTriangle className="h-3 w-3" />
      default:
        return <Clock className="h-3 w-3" />
    }
  }

  const getLeaveTypeIcon = (type: string): void => {
    switch (type) {
      case 'annual':
        return <Plane className="h-3 w-3" />
      case 'sick':
        return <Heart className="h-3 w-3" />
      case 'emergency':
        return <AlertTriangle className="h-3 w-3" />
      case 'maternity':
        return <User className="h-3 w-3" />
      case 'paternity':
        return <User className="h-3 w-3" />
      case 'unpaid':
        return <Briefcase className="h-3 w-3" />
      default:
        return <Calendar className="h-3 w-3" />
    }
  }

  const calculateDuration = (startDate: string, endDate: string): void => {
    const start = new Date(startDate)
    const end = new Date(endDate)
    const diffTime = Math.abs(end.getTime() - start.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1
    return diffDays
  }

  // Table columns configuration
  const columns: TableColumn<EmployeeLeave>[] = [
    {
      key: 'type',
      label: t.type,
      sortable: true,
      render: (item: EmployeeLeave) => (
        <div className="flex items-center gap-3">
          <div className="p-2 rounded-lg bg-gradient-to-r from-blue-500 to-purple-500 text-white">
            {getLeaveTypeIcon(item.type)}
          </div>
          <div>
            <div className="font-medium text-white">
              {language === 'ar' ? item.typeAr : t[item.type as keyof typeof t]}
            </div>
            <div className="text-sm text-white/60">
              {item.duration} {t.days}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: EmployeeLeave) => (
        <div className="flex items-center gap-1">
          {getStatusIcon(item.status)}
          <Badge className={getStatusColor(item.status)}>
            {t[item.status as keyof typeof t]}
          </Badge>
        </div>
      )
    },
    {
      key: 'startDate',
      label: t.startDate,
      sortable: true,
      render: (item: EmployeeLeave) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">{item.startDate}</span>
        </div>
      )
    },
    {
      key: 'endDate',
      label: t.endDate,
      sortable: true,
      render: (item: EmployeeLeave) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">{item.endDate}</span>
        </div>
      )
    },
    {
      key: 'duration',
      label: t.duration,
      sortable: true,
      render: (item: EmployeeLeave) => (
        <div className="flex items-center gap-1">
          <Clock className="h-3 w-3 text-purple-400" />
          <span className="text-white/80">{item.duration} {t.days}</span>
        </div>
      )
    },
    {
      key: 'reason',
      label: t.reason,
      render: (item: EmployeeLeave) => (
        <div className="max-w-xs truncate text-white/80">
          {language === 'ar' ? item.reasonAr : item.reason}
        </div>
      )
    },
    {
      key: 'requestDate',
      label: t.requestDate,
      sortable: true,
      render: (item: EmployeeLeave) => (
        <div className="flex items-center gap-1">
          <CalendarDays className="h-3 w-3 text-green-400" />
          <span className="text-white/80">{item.requestDate}</span>
        </div>
      )
    },
    {
      key: 'approvedBy',
      label: t.approvedBy,
      render: (item: EmployeeLeave) => (
        <div className="flex items-center gap-1">
          <User className="h-3 w-3 text-yellow-400" />
          <span className="text-white/80">
            {item.approvedBy ? (language === 'ar' ? item.approvedByAr : item.approvedBy) : '-'}
          </span>
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction<EmployeeLeave>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: EmployeeLeave) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: EmployeeLeave) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: EmployeeLeave) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.pending, value: 'pending' },
        { label: t.approved, value: 'approved' },
        { label: t.rejected, value: 'rejected' }
      ]
    },
    {
      key: 'type',
      label: t.type,
      options: [
        { label: t.annual, value: 'annual' },
        { label: t.sick, value: 'sick' },
        { label: t.emergency, value: 'emergency' },
        { label: t.maternity, value: 'maternity' },
        { label: t.paternity, value: 'paternity' },
        { label: t.unpaid, value: 'unpaid' }
      ]
    }
  ]

  // Form fields configuration - FIXED: Simplified to essential fields only
  const formFields: FormField[] = [
    {
      name: 'type',
      label: t.type,
      type: 'select',
      required: true,
      options: [
        { label: t.annual, value: 'annual' },
        { label: t.sick, value: 'sick' },
        { label: t.emergency, value: 'emergency' },
        { label: t.maternity, value: 'maternity' },
        { label: t.paternity, value: 'paternity' },
        { label: t.unpaid, value: 'unpaid' }
      ]
    },
    {
      name: 'startDate',
      label: t.startDate,
      type: 'date',
      required: true
    },
    {
      name: 'endDate',
      label: t.endDate,
      type: 'date',
      required: true
    },
    {
      name: 'duration',
      label: t.duration,
      type: 'number',
      required: true,
      min: 1,
      placeholder: language === 'ar' ? 'عدد الأيام' : 'Number of days'
    },
    {
      name: 'reason',
      label: t.reason,
      type: 'textarea',
      required: true,
      rows: 3,
      placeholder: language === 'ar' ? 'اكتب سبب طلب الإجازة...' : 'Enter reason for leave request...'
    },
    {
      name: 'reasonAr',
      label: t.reason + ' (عربي)',
      type: 'textarea',
      required: false, // Made optional
      rows: 3,
      placeholder: 'اكتب السبب باللغة العربية (اختياري)...'
    }
  ]

  // Event handlers
  const handleCreate = (): void => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = (): void => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<EmployeeLeave>) => {
    try {
      // Calculate duration if start and end dates are provided
      if (data.startDate && data.endDate) {
        data.duration = calculateDuration(data.startDate, data.endDate)
      }

      // Auto-populate Arabic leave type based on selected type
      const typeArMapping: Record<string, string> = {
        'annual': 'إجازة سنوية',
        'sick': 'إجازة مرضية',
        'emergency': 'إجازة طارئة',
        'maternity': 'إجازة أمومة',
        'paternity': 'إجازة أبوة',
        'unpaid': 'إجازة بدون راتب'
      }

      if (modalMode === 'create') {
        await createItem({
          ...data,
          typeAr: typeArMapping[data.type as string] || data.type,
          requestDate: new Date().toISOString().split('T')[0],
          status: 'pending'
        })
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, {
          ...data,
          typeAr: typeArMapping[data.type as string] || data.typeAr || selectedItem.typeAr
        })
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('csv')
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.employeeLeave}
        data={leaveRequests}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addLeave : modalMode === 'edit' ? t.editLeave : t.view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
