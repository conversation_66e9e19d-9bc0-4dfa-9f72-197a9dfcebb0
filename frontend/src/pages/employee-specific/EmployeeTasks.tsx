/**
 * Employee Tasks Page - READ-ONLY Implementation
 * Restricted version for employees - can only view assigned tasks
 * No create, edit, delete, or export capabilities
 */

import React, { useState, ReactElement } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  CheckSquare,
  Clock,
  Calendar,
  User,
  Flag,
  Eye,
  CheckCircle,
  AlertTriangle,
  Target,
  BarChart3,
  RefreshCw
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { employeeTaskService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface EmployeeTasksProps {
  language: 'ar' | 'en'
}

interface EmployeeTask {
  id: number
  name: string
  nameAr: string
  description: string
  descriptionAr: string
  dueDate: string
  priority: 'high' | 'medium' | 'low'
  status: 'pending' | 'inProgress' | 'completed' | 'overdue'
  project: string
  projectAr: string
  assignedBy: string
  assignedByAr: string
  progress: number
  estimatedHours: number
  spentHours: number
  startDate?: string
  completedDate?: string
}

const translations = {
  ar: {
    employeeTasks: 'مهامي - الموظف',
    addTask: 'إضافة مهمة',
    editTask: 'تعديل المهمة',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذه المهمة؟',
    searchPlaceholder: 'البحث في المهام...',
    name: 'اسم المهمة',
    description: 'الوصف',
    dueDate: 'تاريخ الاستحقاق',
    priority: 'الأولوية',
    status: 'الحالة',
    project: 'المشروع',
    assignedBy: 'مُعين من',
    progress: 'التقدم',
    estimatedHours: 'الساعات المقدرة',
    spentHours: 'الساعات المنفقة',
    startDate: 'تاريخ البداية',
    completedDate: 'تاريخ الإكمال',
    pending: 'معلق',
    inProgress: 'قيد التنفيذ',
    completed: 'مكتمل',
    overdue: 'متأخر',
    high: 'عالي',
    medium: 'متوسط',
    low: 'منخفض'
  },
  en: {
    employeeTasks: 'My Tasks - Employee',
    addTask: 'Add Task',
    editTask: 'Edit Task',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this task?',
    searchPlaceholder: 'Search tasks...',
    name: 'Task Name',
    description: 'Description',
    dueDate: 'Due Date',
    priority: 'Priority',
    status: 'Status',
    project: 'Project',
    assignedBy: 'Assigned By',
    progress: 'Progress',
    estimatedHours: 'Estimated Hours',
    spentHours: 'Spent Hours',
    startDate: 'Start Date',
    completedDate: 'Completed Date',
    pending: 'Pending',
    inProgress: 'In Progress',
    completed: 'Completed',
    overdue: 'Overdue',
    high: 'High',
    medium: 'Medium',
    low: 'Low'
  }
}

export default function EmployeeTasks({ language }: EmployeeTasksProps): React.ReactElement {
  const [showModal, setShowModal] = useState<boolean>(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: tasks,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<EmployeeTask>({
    service: employeeTaskService,
    autoLoad: true,
    pageSize: 20
  })

  // Check if error indicates missing employee record
  const isMissingEmployeeRecord = error && (
    error.includes('No employee record found') ||
    error.includes('Employee.DoesNotExist') ||
    tasks?.length === 0 && !loading
  )

  // Helper functions
  const getStatusColor = (status: string): void => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'inProgress':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'overdue':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getPriorityColor = (priority: string): void => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'low':
        return 'bg-green-100 text-green-800 border-green-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusIcon = (status: string): void => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-3 w-3" />
      case 'inProgress':
        return <Clock className="h-3 w-3 animate-pulse" />
      case 'pending':
        return <Target className="h-3 w-3" />
      case 'overdue':
        return <AlertTriangle className="h-3 w-3" />
      default:
        return <Clock className="h-3 w-3" />
    }
  }

  const getPriorityIcon = (priority: string): void => {
    switch (priority) {
      case 'high':
        return <Flag className="h-3 w-3" />
      case 'medium':
        return <Flag className="h-3 w-3" />
      case 'low':
        return <Flag className="h-3 w-3" />
      default:
        return <Flag className="h-3 w-3" />
    }
  }

  const getProgressColor = (progress: number): void => {
    if (progress >= 90) return 'bg-green-500'
    if (progress >= 70) return 'bg-blue-500'
    if (progress >= 50) return 'bg-yellow-500'
    return 'bg-red-500'
  }

  const isOverdue = (dueDate: string, status: string): void => {
    if (status === 'completed') return false
    const due = new Date(dueDate)
    const today = new Date()
    return due < today
  }

  // Table columns configuration
  const columns: TableColumn<EmployeeTask>[] = [
    {
      key: 'name',
      label: t.name,
      sortable: true,
      render: (item: EmployeeTask) => (
        <div className="flex items-center gap-3">
          <div className="p-2 rounded-lg bg-gradient-to-r from-blue-500 to-purple-500 text-white">
            <CheckSquare className="h-4 w-4" />
          </div>
          <div>
            <div className="font-medium text-white">
              {language === 'ar' ? item.nameAr : item.name}
            </div>
            <div className="text-sm text-white/60">
              {language === 'ar' ? item.descriptionAr : item.description}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: EmployeeTask) => (
        <div className="flex items-center gap-1">
          {getStatusIcon(item.status)}
          <Badge className={getStatusColor(item.status)}>
            {t[item.status as keyof typeof t]}
          </Badge>
          {isOverdue(item.dueDate, item.status) && (
            <AlertTriangle className="h-4 w-4 text-red-400 ml-1" />
          )}
        </div>
      )
    },
    {
      key: 'priority',
      label: t.priority,
      sortable: true,
      render: (item: EmployeeTask) => (
        <div className="flex items-center gap-1">
          {getPriorityIcon(item.priority)}
          <Badge className={getPriorityColor(item.priority)}>
            {t[item.priority as keyof typeof t]}
          </Badge>
        </div>
      )
    },
    {
      key: 'progress',
      label: t.progress,
      sortable: true,
      render: (item: EmployeeTask) => (
        <div className="flex items-center gap-2">
          <div className="w-16 bg-white/20 rounded-full h-2">
            <div
              className={`h-2 rounded-full ${getProgressColor(item.progress)}`}
              style={{ width: `${item.progress}%` }}
            ></div>
          </div>
          <span className="text-white text-sm font-medium">{item.progress}%</span>
        </div>
      )
    },
    {
      key: 'project',
      label: t.project,
      render: (item: EmployeeTask) => (
        <div className="flex items-center gap-1">
          <BarChart3 className="h-3 w-3 text-purple-400" />
          <span className="text-white/80">
            {language === 'ar' ? item.projectAr : item.project}
          </span>
        </div>
      )
    },
    {
      key: 'assignedBy',
      label: t.assignedBy,
      render: (item: EmployeeTask) => (
        <div className="flex items-center gap-1">
          <User className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">
            {language === 'ar' ? item.assignedByAr : item.assignedBy}
          </span>
        </div>
      )
    },
    {
      key: 'dueDate',
      label: t.dueDate,
      sortable: true,
      render: (item: EmployeeTask) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-blue-400" />
          <span className={`text-sm ${isOverdue(item.dueDate, item.status) ? 'text-red-400' : 'text-white/80'}`}>
            {item.dueDate}
          </span>
        </div>
      )
    },
    {
      key: 'hours',
      label: 'Hours',
      render: (item: EmployeeTask) => (
        <div>
          <div className="text-white text-sm">
            {item.spentHours}h / {item.estimatedHours}h
          </div>
          <div className="text-white/60 text-xs">
            {Math.round((item.spentHours / item.estimatedHours) * 100)}% used
          </div>
        </div>
      )
    }
  ]

  // Table actions configuration - RESTRICTED for employees
  const actions: TableAction<EmployeeTask>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: EmployeeTask) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    // REMOVED: Edit capability - employees cannot edit tasks
    // REMOVED: Delete capability - employees cannot delete tasks
    {
      label: 'Mark Complete',
      icon: CheckCircle,
      onClick: async (item: EmployeeTask) => {
        if (item.status !== 'completed') {
          await updateItem(item.id, {
            ...item,
            status: 'completed',
            progress: 100,
            completedDate: new Date().toISOString().split('T')[0]
          })
        }
      },
      variant: 'ghost',
      className: 'text-green-400 hover:text-green-300 hover:bg-green-500/20',
      // Only show for tasks assigned to current user and not completed
      condition: (item: EmployeeTask) => item.status !== 'completed'
    }
    // REMOVED: Delete action - employees cannot delete tasks
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.pending, value: 'pending' },
        { label: t.inProgress, value: 'inProgress' },
        { label: t.completed, value: 'completed' },
        { label: t.overdue, value: 'overdue' }
      ]
    },
    {
      key: 'priority',
      label: t.priority,
      options: [
        { label: t.high, value: 'high' },
        { label: t.medium, value: 'medium' },
        { label: t.low, value: 'low' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'name',
      label: t.name,
      type: 'text',
      required: true
    },
    {
      name: 'nameAr',
      label: t.name + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'description',
      label: t.description,
      type: 'textarea',
      required: true
    },
    {
      name: 'descriptionAr',
      label: t.description + ' (عربي)',
      type: 'textarea',
      required: true
    },
    {
      name: 'dueDate',
      label: t.dueDate,
      type: 'date',
      required: true
    },
    {
      name: 'priority',
      label: t.priority,
      type: 'select',
      required: true,
      options: [
        { label: t.high, value: 'high' },
        { label: t.medium, value: 'medium' },
        { label: t.low, value: 'low' }
      ]
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: t.pending, value: 'pending' },
        { label: t.inProgress, value: 'inProgress' },
        { label: t.completed, value: 'completed' },
        { label: t.overdue, value: 'overdue' }
      ]
    },
    {
      name: 'project',
      label: t.project,
      type: 'text',
      required: true
    },
    {
      name: 'projectAr',
      label: t.project + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'assignedBy',
      label: t.assignedBy,
      type: 'text',
      required: true
    },
    {
      name: 'assignedByAr',
      label: t.assignedBy + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'progress',
      label: t.progress,
      type: 'number',
      required: true,
      min: 0,
      max: 100
    },
    {
      name: 'estimatedHours',
      label: t.estimatedHours,
      type: 'number',
      required: true,
      min: 0
    },
    {
      name: 'spentHours',
      label: t.spentHours,
      type: 'number',
      required: true,
      min: 0
    },
    {
      name: 'startDate',
      label: t.startDate,
      type: 'date'
    },
    {
      name: 'completedDate',
      label: t.completedDate,
      type: 'date'
    }
  ]

  // Event handlers - RESTRICTED for employees
  // REMOVED: handleCreate - employees cannot create tasks

  const handleModalClose = (): void => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  // REMOVED: handleSave - employees cannot create/edit tasks (only mark complete)
  // REMOVED: handleExport - employees cannot export task data

  // Show helpful message if employee record is missing
  if (isMissingEmployeeRecord) {
    return (
      <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
        <div className="glass-card border-white/20 shadow-2xl p-8 text-center">
          <div className="mb-6">
            <CheckSquare className="w-16 h-16 text-blue-400 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-white mb-2">
              {language === 'ar' ? 'إعداد الملف الشخصي مطلوب' : 'Profile Setup Required'}
            </h2>
            <p className="text-white/80 mb-6">
              {language === 'ar'
                ? 'لعرض مهامك، يجب إكمال إعداد ملفك الشخصي كموظف. يرجى التواصل مع إدارة الموارد البشرية لإكمال عملية التسجيل.'
                : 'To view your tasks, your employee profile needs to be completed. Please contact HR to complete your registration process.'
              }
            </p>
          </div>

          <div className="bg-blue-500/20 border border-blue-500 rounded-lg p-4 mb-6">
            <h3 className="text-blue-400 font-semibold mb-2">
              {language === 'ar' ? 'الخطوات التالية:' : 'Next Steps:'}
            </h3>
            <ul className="text-white/80 text-sm space-y-1">
              <li>
                {language === 'ar'
                  ? '• تواصل مع قسم الموارد البشرية'
                  : '• Contact the HR department'
                }
              </li>
              <li>
                {language === 'ar'
                  ? '• قدم المعلومات المطلوبة لإكمال ملفك الشخصي'
                  : '• Provide required information to complete your profile'
                }
              </li>
              <li>
                {language === 'ar'
                  ? '• انتظر تأكيد التفعيل من الإدارة'
                  : '• Wait for activation confirmation from management'
                }
              </li>
            </ul>
          </div>

          <button
            onClick={refresh}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors duration-200 flex items-center gap-2 mx-auto"
          >
            <RefreshCw className="w-4 h-4" />
            {language === 'ar' ? 'تحديث' : 'Refresh'}
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* READ-ONLY Table for Employee Tasks */}
      <CrudTable
        title={t.employeeTasks}
        data={tasks}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        // REMOVED: onCreate - employees cannot create tasks
        onRefresh={refresh}
        // REMOVED: onExport - employees cannot export data
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
        showCreateButton={false}
        showExportButton={false}
      />

      {/* VIEW-ONLY Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        // REMOVED: onSave - employees cannot save/edit tasks
        title={t.view}
        fields={formFields}
        initialData={selectedItem as unknown}
        language={language}
        loading={false}
        readOnly={true}
      />
    </div>
  )
}
