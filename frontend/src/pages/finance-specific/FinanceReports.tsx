/**
 * Finance Reports Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  FileText,
  DollarSign,
  TrendingUp,
  TrendingDown,
  BarChart3,
  Calculator,
  Target,
  Download,
  Eye,
  Edit,
  Trash2,
  Calendar,
  Clock,
  Wallet,
  Receipt,
  AlertTriangle,
  CheckCircle
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { financeReportService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface FinanceReportsProps {
  language: 'ar' | 'en'
}

interface FinanceReport {
  id: number
  title: string
  titleAr: string
  description: string
  descriptionAr: string
  type: 'profitloss' | 'cashflow' | 'balancesheet' | 'expenses' | 'revenue' | 'budget'
  status: 'ready' | 'processing' | 'failed' | 'scheduled'
  lastGenerated: string
  size: string
  downloads: number
  amount: number
  variance: string
  period: string
  createdBy: string
  createdByAr: string
  scheduledDate?: string
  format: 'pdf' | 'excel' | 'csv'
}

const translations = {
  ar: {
    financeReports: 'التقارير المالية - المالية',
    addReport: 'إضافة تقرير',
    editReport: 'تعديل التقرير',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذا التقرير؟',
    searchPlaceholder: 'البحث في التقارير...',
    title: 'عنوان التقرير',
    description: 'الوصف',
    type: 'نوع التقرير',
    status: 'الحالة',
    lastGenerated: 'آخر إنشاء',
    size: 'حجم التقرير',
    downloads: 'التحميلات',
    amount: 'المبلغ',
    variance: 'التباين',
    period: 'الفترة',
    createdBy: 'أنشأ بواسطة',
    scheduledDate: 'تاريخ الجدولة',
    format: 'التنسيق',
    profitloss: 'الأرباح والخسائر',
    cashflow: 'التدفق النقدي',
    balancesheet: 'الميزانية العمومية',
    expenses: 'المصروفات',
    revenue: 'الإيرادات',
    budget: 'الميزانية',
    ready: 'جاهز',
    processing: 'قيد المعالجة',
    failed: 'فشل',
    scheduled: 'مجدول',
    pdf: 'PDF',
    excel: 'Excel',
    csv: 'CSV'
  },
  en: {
    financeReports: 'Finance Reports',
    addReport: 'Add Report',
    editReport: 'Edit Report',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this report?',
    searchPlaceholder: 'Search reports...',
    title: 'Report Title',
    description: 'Description',
    type: 'Report Type',
    status: 'Status',
    lastGenerated: 'Last Generated',
    size: 'Report Size',
    downloads: 'Downloads',
    amount: 'Amount',
    variance: 'Variance',
    period: 'Period',
    createdBy: 'Created By',
    scheduledDate: 'Scheduled Date',
    format: 'Format',
    profitloss: 'Profit & Loss',
    cashflow: 'Cash Flow',
    balancesheet: 'Balance Sheet',
    expenses: 'Expenses',
    revenue: 'Revenue',
    budget: 'Budget',
    ready: 'Ready',
    processing: 'Processing',
    failed: 'Failed',
    scheduled: 'Scheduled',
    pdf: 'PDF',
    excel: 'Excel',
    csv: 'CSV'
  }
}

export default function FinanceReports({ language }: FinanceReportsProps): React.ReactElement {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: reports,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<FinanceReport>({
    service: financeReportService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getStatusColor = (status: string): void => {
    if (!status) return 'bg-gray-100 text-gray-800 border-gray-200'
    switch (status) {
      case 'ready':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'processing':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'failed':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'scheduled':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusIcon = (status: string): void => {
    if (!status) return <FileText className="h-4 w-4" />
    switch (status) {
      case 'ready':
        return <CheckCircle className="h-4 w-4" />
      case 'processing':
        return <Clock className="h-4 w-4 animate-spin" />
      case 'failed':
        return <AlertTriangle className="h-4 w-4" />
      case 'scheduled':
        return <Calendar className="h-4 w-4" />
      default:
        return <FileText className="h-4 w-4" />
    }
  }

  const getVarianceColor = (variance: string): void => {
    if (!variance) return 'text-blue-400'
    if (variance.startsWith('+')) return 'text-green-400'
    if (variance.startsWith('-')) return 'text-red-400'
    return 'text-blue-400'
  }

  const getTypeIcon = (type: string): void => {
    if (!type) return <FileText className="h-4 w-4" />
    switch (type) {
      case 'profitloss':
        return <TrendingUp className="h-4 w-4" />
      case 'cashflow':
        return <Wallet className="h-4 w-4" />
      case 'balancesheet':
        return <Calculator className="h-4 w-4" />
      case 'expenses':
        return <Receipt className="h-4 w-4" />
      case 'revenue':
        return <DollarSign className="h-4 w-4" />
      case 'budget':
        return <Target className="h-4 w-4" />
      default:
        return <FileText className="h-4 w-4" />
    }
  }

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount)
  }

  // Table columns configuration
  const columns: TableColumn<FinanceReport>[] = [
    {
      key: 'title',
      label: t.title,
      sortable: true,
      render: (item: FinanceReport) => (
        <div className="flex items-center gap-3">
          <div className="p-2 rounded-lg bg-gradient-to-r from-blue-500 to-purple-500 text-white">
            {getTypeIcon(item.type)}
          </div>
          <div>
            <div className="font-medium text-white">
              {language === 'ar' ? (item.titleAr || item.title || 'Untitled') : (item.title || 'Untitled')}
            </div>
            <div className="text-sm text-white/60">
              {language === 'ar' ? (item.descriptionAr || item.description || '') : (item.description || '')}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'type',
      label: t.type,
      sortable: true,
      render: (item: FinanceReport) => (
        <div className="flex items-center gap-1">
          {getTypeIcon(item.type || 'default')}
          <span className="text-white/80">{t[item.type as keyof typeof t] || item.type || '-'}</span>
        </div>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: FinanceReport) => (
        <div className="flex items-center gap-1">
          {getStatusIcon(item.status || 'default')}
          <Badge className={getStatusColor(item.status || 'default')}>
            {t[item.status as keyof typeof t] || item.status || '-'}
          </Badge>
        </div>
      )
    },
    {
      key: 'amount',
      label: t.amount,
      sortable: true,
      render: (item: FinanceReport) => (
        <div className="text-white font-medium">
          {formatCurrency(item.amount || 0)}
        </div>
      )
    },
    {
      key: 'variance',
      label: t.variance,
      render: (item: FinanceReport) => (
        <span className={`font-medium ${getVarianceColor(item.variance || '')}`}>
          {item.variance || '-'}
        </span>
      )
    },
    {
      key: 'lastGenerated',
      label: t.lastGenerated,
      sortable: true,
      render: (item: FinanceReport) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">{item.lastGenerated}</span>
        </div>
      )
    },
    {
      key: 'size',
      label: t.size,
      render: (item: FinanceReport) => (
        <span className="text-white/80">{item.size}</span>
      )
    },
    {
      key: 'downloads',
      label: t.downloads,
      sortable: true,
      render: (item: FinanceReport) => (
        <div className="flex items-center gap-1">
          <Download className="h-3 w-3 text-green-400" />
          <span className="text-white/80">{item.downloads}</span>
        </div>
      )
    },
    {
      key: 'format',
      label: t.format,
      render: (item: FinanceReport) => (
        <Badge variant="outline" className="text-white border-white/20">
          {t[item.format as keyof typeof t]}
        </Badge>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction<FinanceReport>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: FinanceReport) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: FinanceReport) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: 'Download',
      icon: Download,
      onClick: (item: FinanceReport) => {
        // Handle download logic
        console.log('Download report:', item.id)
      },
      variant: 'ghost',
      className: 'text-blue-400 hover:text-blue-300 hover:bg-blue-500/20'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: FinanceReport) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'type',
      label: t.type,
      options: [
        { label: t.profitloss, value: 'profitloss' },
        { label: t.cashflow, value: 'cashflow' },
        { label: t.balancesheet, value: 'balancesheet' },
        { label: t.expenses, value: 'expenses' },
        { label: t.revenue, value: 'revenue' },
        { label: t.budget, value: 'budget' }
      ]
    },
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.ready, value: 'ready' },
        { label: t.processing, value: 'processing' },
        { label: t.failed, value: 'failed' },
        { label: t.scheduled, value: 'scheduled' }
      ]
    },
    {
      key: 'format',
      label: t.format,
      options: [
        { label: t.pdf, value: 'pdf' },
        { label: t.excel, value: 'excel' },
        { label: t.csv, value: 'csv' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'title',
      label: t.title,
      type: 'text',
      required: true
    },
    {
      name: 'titleAr',
      label: t.title + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'description',
      label: t.description,
      type: 'textarea',
      required: true
    },
    {
      name: 'descriptionAr',
      label: t.description + ' (عربي)',
      type: 'textarea',
      required: true
    },
    {
      name: 'type',
      label: t.type,
      type: 'select',
      required: true,
      options: [
        { label: t.profitloss, value: 'profitloss' },
        { label: t.cashflow, value: 'cashflow' },
        { label: t.balancesheet, value: 'balancesheet' },
        { label: t.expenses, value: 'expenses' },
        { label: t.revenue, value: 'revenue' },
        { label: t.budget, value: 'budget' }
      ]
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: t.ready, value: 'ready' },
        { label: t.processing, value: 'processing' },
        { label: t.failed, value: 'failed' },
        { label: t.scheduled, value: 'scheduled' }
      ]
    },
    {
      name: 'amount',
      label: t.amount,
      type: 'number',
      required: true,
      min: 0
    },
    {
      name: 'variance',
      label: t.variance,
      type: 'text',
      required: true,
      placeholder: 'e.g., +12.5%, -3.2%'
    },
    {
      name: 'period',
      label: t.period,
      type: 'text',
      required: true,
      placeholder: 'e.g., Q1 2024, January 2024'
    },
    {
      name: 'size',
      label: t.size,
      type: 'text',
      placeholder: 'e.g., 3.2 MB'
    },
    {
      name: 'downloads',
      label: t.downloads,
      type: 'number',
      min: 0
    },
    {
      name: 'format',
      label: t.format,
      type: 'select',
      required: true,
      options: [
        { label: t.pdf, value: 'pdf' },
        { label: t.excel, value: 'excel' },
        { label: t.csv, value: 'csv' }
      ]
    },
    {
      name: 'createdBy',
      label: t.createdBy,
      type: 'text',
      required: true
    },
    {
      name: 'createdByAr',
      label: t.createdBy + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'lastGenerated',
      label: t.lastGenerated,
      type: 'date',
      required: true
    },
    {
      name: 'scheduledDate',
      label: t.scheduledDate,
      type: 'date'
    }
  ]

  // Event handlers
  const handleCreate = (): void => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = (): void => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<FinanceReport>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('csv')
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.financeReports}
        data={reports}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addReport : modalMode === 'edit' ? t.editReport : t.view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
