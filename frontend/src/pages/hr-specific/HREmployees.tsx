/**
 * HR Employees Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  Users,
  UserCheck,
  Calendar,
  Award,
  AlertTriangle,
  CheckCircle,
  Eye,
  Edit,
  Trash2,
  User,
  Mail,
  Phone,
  MapPin,
  Clock,
  Shield,
  X,
  Check
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { useAuth } from '@/hooks/useAuth'
import { hrEmployeesService } from '@/services/crudService'
import { employeeAPI, Employee } from '@/services/employeeAPI'
import { employeeApprovalService, PendingEmployee } from '@/services/employeeApprovalService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import EnhancedCrudModal, { EnhancedFormField } from '@/components/common/EnhancedCrudModal'
import { enhancedAPI } from '@/services/enhancedAPI'
import { employeeValidationSchema } from '@/utils/validation'

interface HREmployeesProps {
  language: 'ar' | 'en'
}

const translations = {
  ar: {
    hrEmployees: 'إدارة الموظفين - الموارد البشرية',
    addEmployee: 'إضافة موظف',
    editEmployee: 'تعديل الموظف',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذا الموظف؟',
    searchPlaceholder: 'البحث في الموظفين...',
    name: 'الاسم',
    email: 'البريد الإلكتروني',
    phone: 'الهاتف',
    department: 'القسم',
    position: 'المنصب',
    status: 'الحالة',
    salary: 'الراتب',
    joinDate: 'تاريخ الانضمام',
    address: 'العنوان',
    emergencyContact: 'جهة الاتصال الطارئة',
    nationalId: 'رقم الهوية الوطنية',
    active: 'نشط',
    inactive: 'غير نشط',
    onLeave: 'في إجازة'
  },
  en: {
    hrEmployees: 'HR Employee Management',
    addEmployee: 'Add Employee',
    editEmployee: 'Edit Employee',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this employee?',
    searchPlaceholder: 'Search employees...',
    name: 'Name',
    email: 'Email',
    phone: 'Phone',
    department: 'Department',
    position: 'Position',
    status: 'Status',
    salary: 'Salary',
    joinDate: 'Join Date',
    address: 'Address',
    emergencyContact: 'Emergency Contact',
    nationalId: 'National ID',
    active: 'Active',
    inactive: 'Inactive',
    onLeave: 'On Leave'
  }
}

export default function HREmployees({ language }: HREmployeesProps): React.ReactElement {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')
  const [pendingApprovals, setPendingApprovals] = useState<PendingEmployee[]>([])
  const [showApprovals, setShowApprovals] = useState(false)
  const [approvingId, setApprovingId] = useState<number | null>(null)
  const [rejectingId, setRejectingId] = useState<number | null>(null)

  const t = translations[language]
  const isRTL = language === 'ar'

  // Get current user's role for permission checking
  const { user } = useAuth()
  const currentUserRole = user?.role?.name || 'EMPLOYEE'
  const isAdmin = currentUserRole === 'ADMIN' || currentUserRole === 'SUPERADMIN'

  // ENHANCED: Use the generic CRUD hook with proper error handling
  const {
    items: employees,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<Employee>({
    service: hrEmployeesService as any,
    autoLoad: true,
    pageSize: 20,
    entityType: 'employee',
    enableInvalidation: false, // Disable to prevent excessive refreshes
    debounceMs: 300 // Add debounce to search
  })

  // FIXED: Memoized helper functions to prevent unnecessary re-renders
  const getStatusColor = useCallback((status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'inactive':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'onLeave':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }, [])



  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount)
  }

  // FIXED: Memoized event handlers to prevent unnecessary re-renders
  const handleCreate = useCallback(() => {
    setModalMode('create')
    setShowModal(true)
  }, [])

  const handleModalClose = useCallback(() => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }, [selectItem, clearError])

  const handleSave = useCallback(async (data: Partial<Employee>) => {
    try {
      let result

      if (modalMode === 'create') {
        result = await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        result = await updateItem(selectedItem.id, data)
      }

      if (result?.success !== false) {
        setShowModal(false)
        selectItem(null)

        // Only refresh if the operation was successful
        if (result) {
          await refresh()
        }

        const successMessage = modalMode === 'create'
          ? (language === 'ar' ? 'تم إنشاء الموظف بنجاح' : 'Employee created successfully')
          : (language === 'ar' ? 'تم تحديث الموظف بنجاح' : 'Employee updated successfully')

        alert(successMessage)
      } else {
        throw new Error(result?.error || 'Save operation failed')
      }
    } catch (error) {
      console.error('Save error:', error)
      const errorMessage = modalMode === 'create'
        ? (language === 'ar' ? 'فشل في إنشاء الموظف' : 'Failed to create employee')
        : (language === 'ar' ? 'فشل في تحديث الموظف' : 'Failed to update employee')

      alert(errorMessage)
    }
  }, [modalMode, selectedItem, createItem, updateItem, refresh, language])

  const handleDelete = useCallback(async (employee: Employee) => {
    try {
      const confirmMessage = language === 'ar'
        ? `هل أنت متأكد من حذف الموظف ${employee.first_name} ${employee.last_name}؟`
        : `Are you sure you want to delete employee ${employee.first_name} ${employee.last_name}?`

      if (window.confirm(confirmMessage)) {
        console.log(`🗑️ Deleting employee ${employee.id}`)
        const result = await deleteItem(employee.id)

        if (result?.success !== false) {
          console.log(`✅ Successfully deleted employee ${employee.id}`)
          // Refresh the list to show updated data
          await refresh()
        } else {
          throw new Error(result?.error || 'Delete operation failed')
        }
      }
    } catch (error) {
      console.error('❌ Delete error:', error)
      const errorMessage = language === 'ar'
        ? 'فشل في حذف الموظف'
        : 'Failed to delete employee'

      alert(errorMessage)
    }
  }, [deleteItem, language, refresh])

  const handleExport = async () => {
    try {
      await exportData('excel')
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  // Load pending approvals for admin users
  const loadPendingApprovals = useCallback(async () => {
    if (!isAdmin) return

    try {
      const pending = await employeeApprovalService.getPendingApprovals()
      setPendingApprovals(pending)
    } catch (error) {
      console.error('Error loading pending approvals:', error)
    }
  }, [isAdmin])

  // Load pending approvals on component mount
  useEffect(() => {
    loadPendingApprovals()
  }, [loadPendingApprovals])

  // Handle employee approval
  const handleApprove = useCallback(async (employeeId: number) => {
    try {
      setApprovingId(employeeId)
      await employeeApprovalService.approveEmployee(employeeId)

      const successMessage = language === 'ar'
        ? 'تم الموافقة على الموظف وإرسال بريد التفعيل بنجاح'
        : 'Employee approved and activation email sent successfully'
      alert(successMessage)

      // Refresh pending approvals and employee list
      await loadPendingApprovals()
      await refresh()
    } catch (error) {
      console.error('Error approving employee:', error)
      const errorMessage = language === 'ar'
        ? 'فشل في الموافقة على الموظف'
        : 'Failed to approve employee'
      alert(errorMessage)
    } finally {
      setApprovingId(null)
    }
  }, [language, loadPendingApprovals, refresh])

  // Handle employee rejection
  const handleReject = useCallback(async (employeeId: number) => {
    try {
      const reason = prompt(
        language === 'ar'
          ? 'أدخل سبب الرفض (اختياري):'
          : 'Enter rejection reason (optional):'
      ) || ''

      setRejectingId(employeeId)
      await employeeApprovalService.rejectEmployee(employeeId, reason)

      const successMessage = language === 'ar'
        ? 'تم رفض الموظف بنجاح'
        : 'Employee rejected successfully'
      alert(successMessage)

      // Refresh pending approvals and employee list
      await loadPendingApprovals()
      await refresh()
    } catch (error) {
      console.error('Error rejecting employee:', error)
      const errorMessage = language === 'ar'
        ? 'فشل في رفض الموظف'
        : 'Failed to reject employee'
      alert(errorMessage)
    } finally {
      setRejectingId(null)
    }
  }, [language, loadPendingApprovals, refresh])

  // Table columns configuration
  const columns: TableColumn<Employee>[] = [
    {
      key: 'name',
      label: t.name,
      sortable: true,
      render: (item: Employee) => {
        const displayName = language === 'ar' ? (item.nameAr || item.name) : (item.name || item.nameAr)
        const firstLetter = displayName ? displayName.charAt(0).toUpperCase() : '?'

        return (
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-white font-bold">
              {(item as any).avatar || firstLetter}
            </div>
            <div>
              <div className="font-medium text-white">
                {displayName || 'N/A'}
              </div>
              <div className="text-sm text-white/60 flex items-center gap-1">
                <Mail className="h-3 w-3" />
                {item.email || 'N/A'}
              </div>
            </div>
          </div>
        )
      }
    },
    {
      key: 'department',
      label: t.department,
      sortable: true,
      render: (item: Employee) => {
        const department = language === 'ar' ? (item.departmentAr || item.department) : (item.department || item.departmentAr)
        return (
          <span className="text-white/80">
            {department || 'N/A'}
          </span>
        )
      }
    },
    {
      key: 'position',
      label: t.position,
      render: (item: Employee) => {
        const position = language === 'ar' ? (item.positionAr || item.position) : (item.position || item.positionAr)
        return (
          <span className="text-white/80">
            {position || 'N/A'}
          </span>
        )
      }
    },
    {
      key: 'phone',
      label: t.phone,
      render: (item: Employee) => (
        <div className="flex items-center gap-1">
          <Phone className="h-3 w-3 text-green-400" />
          <span className="text-white/80">{item.phone || 'N/A'}</span>
        </div>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: Employee) => {
        const status = item.status || 'active'
        return (
          <Badge className={getStatusColor(status)}>
            {String(t[status as keyof typeof t])}
          </Badge>
        )
      }
    },

    {
      key: 'salary',
      label: t.salary,
      sortable: true,
      render: (item: Employee) => (
        <span className="text-white font-medium">{formatCurrency(item.salary || 0)}</span>
      )
    },
    {
      key: 'joinDate',
      label: t.joinDate,
      sortable: true,
      render: (item: Employee) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">{item.joinDate || 'N/A'}</span>
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction<Employee>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: Employee) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: Employee) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: (item: Employee) => handleDelete(item),
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // FIXED: Memoized filter options to prevent unnecessary re-renders
  const filterOptions: FilterOption[] = useMemo(() => [
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.active, value: 'active' },
        { label: t.inactive, value: 'inactive' },
        { label: t.onLeave, value: 'onLeave' }
      ]
    },
    {
      key: 'performance',
      label: t.performance,
      options: [
        { label: t.excellent, value: 'excellent' },
        { label: t.good, value: 'good' },
        { label: t.average, value: 'average' },
        { label: t.needsImprovement, value: 'needsImprovement' }
      ]
    },
    {
      key: 'department',
      label: t.department,
      options: [
        { label: 'الموارد البشرية', value: 'hr' },
        { label: 'تقنية المعلومات', value: 'it' },
        { label: 'المالية', value: 'finance' },
        { label: 'المبيعات', value: 'sales' },
        { label: 'التسويق', value: 'marketing' },
        { label: 'العمليات', value: 'operations' }
      ]
    }
  ], [t.status, t.performance, t.department, t.active, t.inactive, t.onLeave, t.excellent, t.good, t.average, t.needsImprovement])

  // FIXED: Memoized form fields configuration to prevent unnecessary re-renders
  const formFields: EnhancedFormField[] = useMemo(() => [
    {
      name: 'firstName',
      label: language === 'ar' ? 'الاسم الأول' : 'First Name',
      type: 'text',
      required: true,
      placeholder: language === 'ar' ? 'أدخل الاسم الأول' : 'Enter first name'
    },
    {
      name: 'lastName',
      label: language === 'ar' ? 'الاسم الأخير' : 'Last Name',
      type: 'text',
      required: true,
      placeholder: language === 'ar' ? 'أدخل الاسم الأخير' : 'Enter last name'
    },
    {
      name: 'email',
      label: t.email,
      type: 'email',
      required: true,
      placeholder: language === 'ar' ? 'أدخل البريد الإلكتروني' : 'Enter email address'
    },
    {
      name: 'phone',
      label: t.phone,
      type: 'tel',
      required: true,
      placeholder: language === 'ar' ? 'أدخل رقم الهاتف' : 'Enter phone number'
    },
    {
      name: 'departmentId',
      label: t.department,
      type: 'select',
      required: true,
      options: [] // This should be populated dynamically from API - TODO: Implement department loading
    },
    {
      name: 'position',
      label: t.position,
      type: 'text',
      required: true,
      placeholder: language === 'ar' ? 'أدخل المنصب' : 'Enter position'
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: t.active, value: 'active' },
        { label: t.inactive, value: 'inactive' },
        { label: t.onLeave, value: 'onLeave' }
      ]
    },

    {
      name: 'salary',
      label: t.salary,
      type: 'number',
      required: true,
      min: 1000,
      max: 1000000,
      placeholder: language === 'ar' ? 'أدخل الراتب' : 'Enter salary'
    },
    {
      name: 'hireDate',
      label: t.joinDate,
      type: 'date',
      required: true
    },
    {
      name: 'address',
      label: t.address,
      type: 'textarea',
      required: true,
      rows: 3,
      placeholder: language === 'ar' ? 'أدخل العنوان' : 'Enter address'
    },
    {
      name: 'emergencyContact',
      label: t.emergencyContact,
      type: 'tel',
      required: true,
      placeholder: language === 'ar' ? 'أدخل رقم الاتصال الطارئ' : 'Enter emergency contact'
    },
    {
      name: 'nationalId',
      label: t.nationalId,
      type: 'text',
      required: true,
      placeholder: language === 'ar' ? 'أدخل رقم الهوية الوطنية' : 'Enter national ID'
    }
  ], [language, t])



  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Admin Approval Section */}
      {isAdmin && (
        <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-blue-400" />
              <h3 className="text-lg font-semibold text-white">
                {language === 'ar' ? 'الموافقات المعلقة' : 'Pending Approvals'}
              </h3>
              <Badge className="bg-blue-500/20 text-blue-400 border-blue-500/30">
                {pendingApprovals.length}
              </Badge>
            </div>
            <button
              onClick={() => setShowApprovals(!showApprovals)}
              className="flex items-center gap-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
            >
              <Clock className="h-4 w-4" />
              {showApprovals
                ? (language === 'ar' ? 'إخفاء' : 'Hide')
                : (language === 'ar' ? 'عرض' : 'Show')
              }
            </button>
          </div>

          {showApprovals && (
            <div className="space-y-3">
              {pendingApprovals.length === 0 ? (
                <p className="text-gray-400 text-center py-4">
                  {language === 'ar' ? 'لا توجد موافقات معلقة' : 'No pending approvals'}
                </p>
              ) : (
                pendingApprovals.map((pending) => (
                  <div key={pending.id} className="bg-gray-700 rounded-lg p-4 flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-gradient-to-r from-orange-500 to-red-500 flex items-center justify-center text-white font-bold">
                          {(language === 'ar' ? pending.name_ar : pending.name).charAt(0).toUpperCase()}
                        </div>
                        <div>
                          <h4 className="font-medium text-white">
                            {language === 'ar' ? pending.name_ar || pending.name : pending.name}
                          </h4>
                          <p className="text-sm text-gray-400">
                            {language === 'ar' ? pending.position_ar || pending.position : pending.position}
                            {pending.department && ` • ${language === 'ar' ? pending.department_ar || pending.department : pending.department}`}
                          </p>
                          <p className="text-xs text-gray-500">
                            {language === 'ar' ? 'أنشئ بواسطة:' : 'Created by:'} {pending.created_by || 'Unknown'}
                          </p>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => handleApprove(pending.id)}
                        disabled={approvingId === pending.id}
                        className="flex items-center gap-1 px-3 py-2 bg-green-600 hover:bg-green-700 disabled:bg-green-600/50 text-white rounded-lg transition-colors"
                      >
                        <Check className="h-4 w-4" />
                        {approvingId === pending.id
                          ? (language === 'ar' ? 'جاري الموافقة...' : 'Approving...')
                          : (language === 'ar' ? 'موافقة' : 'Approve')
                        }
                      </button>
                      <button
                        onClick={() => handleReject(pending.id)}
                        disabled={rejectingId === pending.id}
                        className="flex items-center gap-1 px-3 py-2 bg-red-600 hover:bg-red-700 disabled:bg-red-600/50 text-white rounded-lg transition-colors"
                      >
                        <X className="h-4 w-4" />
                        {rejectingId === pending.id
                          ? (language === 'ar' ? 'جاري الرفض...' : 'Rejecting...')
                          : (language === 'ar' ? 'رفض' : 'Reject')
                        }
                      </button>
                    </div>
                  </div>
                ))
              )}
            </div>
          )}
        </div>
      )}

      {/* CRUD Table */}
      {<CrudTable
        title={t.hrEmployees}
        data={employees}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      /> as any}

      {/* Enhanced CRUD Modal with Validation */}
      <EnhancedCrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addEmployee : modalMode === 'edit' ? t.editEmployee : t.view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        validationSchema={employeeValidationSchema}
      />
    </div>
  )

}
