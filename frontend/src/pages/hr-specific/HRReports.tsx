/**
 * HR Reports Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import { toast } from 'react-hot-toast'
import {
  FileText,
  Users,
  TrendingUp,
  Calendar,
  Clock,
  Download,
  Eye,
  Edit,
  Trash2,
  BarChart3,
  DollarSign,
  UserCheck,
  CheckCircle,
  AlertTriangle
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { hrReportService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface HRReportsProps {
  language: 'ar' | 'en'
}

interface HRReport {
  id: number
  name: string
  nameAr: string
  type: 'employee' | 'performance' | 'attendance' | 'salary' | 'recruitment' | 'turnover' | 'leave' | 'training'
  description: string
  descriptionAr: string
  status: 'ready' | 'processing' | 'failed' | 'scheduled'
  lastGenerated: string
  size: string
  downloads: number
  createdBy: string
  createdByAr: string
  reportPeriod: string
  format: 'pdf' | 'excel' | 'csv'
  isScheduled: boolean
  scheduleFrequency?: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly'
}

const translations = {
  ar: {
    hrReports: 'تقارير الموارد البشرية',
    addReport: 'إضافة تقرير',
    editReport: 'تعديل التقرير',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذا التقرير؟',
    searchPlaceholder: 'البحث في التقارير...',
    name: 'اسم التقرير',
    type: 'نوع التقرير',
    description: 'الوصف',
    status: 'الحالة',
    lastGenerated: 'آخر إنشاء',
    size: 'الحجم',
    downloads: 'التحميلات',
    createdBy: 'أنشأ بواسطة',
    reportPeriod: 'فترة التقرير',
    format: 'التنسيق',
    isScheduled: 'مجدول',
    scheduleFrequency: 'تكرار الجدولة',
    ready: 'جاهز',
    processing: 'قيد المعالجة',
    failed: 'فشل',
    scheduled: 'مجدول',
    employee: 'موظفين',
    performance: 'أداء',
    attendance: 'حضور',
    salary: 'رواتب',
    recruitment: 'توظيف',
    turnover: 'دوران الموظفين',
    leave: 'إجازات',
    training: 'تدريب',
    pdf: 'PDF',
    excel: 'Excel',
    csv: 'CSV',
    daily: 'يومي',
    weekly: 'أسبوعي',
    monthly: 'شهري',
    quarterly: 'ربعي',
    yearly: 'سنوي'
  },
  en: {
    hrReports: 'HR Reports',
    addReport: 'Add Report',
    editReport: 'Edit Report',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this report?',
    searchPlaceholder: 'Search reports...',
    name: 'Report Name',
    type: 'Report Type',
    description: 'Description',
    status: 'Status',
    lastGenerated: 'Last Generated',
    size: 'Size',
    downloads: 'Downloads',
    createdBy: 'Created By',
    reportPeriod: 'Report Period',
    format: 'Format',
    isScheduled: 'Scheduled',
    scheduleFrequency: 'Schedule Frequency',
    ready: 'Ready',
    processing: 'Processing',
    failed: 'Failed',
    scheduled: 'Scheduled',
    employee: 'Employee',
    performance: 'Performance',
    attendance: 'Attendance',
    salary: 'Salary',
    recruitment: 'Recruitment',
    turnover: 'Turnover',
    leave: 'Leave',
    training: 'Training',
    pdf: 'PDF',
    excel: 'Excel',
    csv: 'CSV',
    daily: 'Daily',
    weekly: 'Weekly',
    monthly: 'Monthly',
    quarterly: 'Quarterly',
    yearly: 'Yearly'
  }
}

export default function HRReports({ language }: HRReportsProps): React.ReactElement {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: reports,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<HRReport>({
    service: hrReportService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getStatusColor = (status: string): void => {
    switch (status) {
      case 'ready':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'processing':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'failed':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'scheduled':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getTypeIcon = (type: string): void => {
    switch (type) {
      case 'employee':
        return <Users className="h-4 w-4" />
      case 'performance':
        return <TrendingUp className="h-4 w-4" />
      case 'attendance':
        return <Clock className="h-4 w-4" />
      case 'salary':
        return <DollarSign className="h-4 w-4" />
      case 'recruitment':
        return <UserCheck className="h-4 w-4" />
      case 'turnover':
        return <BarChart3 className="h-4 w-4" />
      case 'leave':
        return <Calendar className="h-4 w-4" />
      case 'training':
        return <CheckCircle className="h-4 w-4" />
      default:
        return <FileText className="h-4 w-4" />
    }
  }

  const getFormatIcon = (format: string): void => {
    switch (format) {
      case 'pdf':
        return <FileText className="h-3 w-3 text-red-400" />
      case 'excel':
        return <BarChart3 className="h-3 w-3 text-green-400" />
      case 'csv':
        return <FileText className="h-3 w-3 text-blue-400" />
      default:
        return <FileText className="h-3 w-3 text-gray-400" />
    }
  }

  // Table columns configuration
  const columns: TableColumn<HRReport>[] = [
    {
      key: 'name',
      label: t.name,
      sortable: true,
      render: (item: HRReport) => {
        const displayName = language === 'ar' ? (item.nameAr || item.name) : (item.name || item.nameAr)
        const format = item.format || 'pdf'

        return (
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-gradient-to-r from-blue-500 to-purple-500 text-white">
              {getTypeIcon(item.type || 'employee')}
            </div>
            <div>
              <div className="font-medium text-white">
                {displayName || 'N/A'}
              </div>
              <div className="text-sm text-white/60 flex items-center gap-1">
                {getFormatIcon(format)}
                {format.toUpperCase()}
              </div>
            </div>
          </div>
        )
      }
    },
    {
      key: 'type',
      label: t.type,
      sortable: true,
      render: (item: HRReport) => {
        const type = item.type || 'employee'
        return (
          <div className="flex items-center gap-2">
            {getTypeIcon(type)}
            <span className="text-white/80">{t[type as keyof typeof t]}</span>
          </div>
        )
      }
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: HRReport) => {
        const status = item.status || 'processing'
        return (
          <Badge className={getStatusColor(status)}>
            {t[status as keyof typeof t]}
          </Badge>
        )
      }
    },
    {
      key: 'lastGenerated',
      label: t.lastGenerated,
      sortable: true,
      render: (item: HRReport) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">{item.lastGenerated || 'N/A'}</span>
        </div>
      )
    },
    {
      key: 'size',
      label: t.size,
      render: (item: HRReport) => (
        <span className="text-white/80">{item.size || 'N/A'}</span>
      )
    },
    {
      key: 'downloads',
      label: t.downloads,
      sortable: true,
      render: (item: HRReport) => (
        <div className="flex items-center gap-1">
          <Download className="h-3 w-3 text-green-400" />
          <span className="text-white font-medium">{item.downloads || 0}</span>
        </div>
      )
    },
    {
      key: 'createdBy',
      label: t.createdBy,
      render: (item: HRReport) => {
        const createdBy = language === 'ar' ? (item.createdByAr || item.createdBy) : (item.createdBy || item.createdByAr)
        return (
          <div className="flex items-center gap-1">
            <UserCheck className="h-3 w-3 text-purple-400" />
            <span className="text-white/80">
              {createdBy || 'N/A'}
            </span>
          </div>
        )
      }
    },
    {
      key: 'reportPeriod',
      label: t.reportPeriod,
      render: (item: HRReport) => (
        <span className="text-white/80">{item.reportPeriod || 'N/A'}</span>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction<HRReport>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: HRReport) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: HRReport) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: 'Download',
      icon: Download,
      onClick: (item: HRReport) => handleDownloadReport(item),
      variant: 'ghost',
      className: 'text-green-400 hover:text-green-300 hover:bg-green-500/20',
      show: (item: HRReport) => item.status === 'completed'  // CRITICAL FIX: Use correct backend status
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: HRReport) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'type',
      label: t.type,
      options: [
        { label: t.employee, value: 'employee' },
        { label: t.performance, value: 'performance' },
        { label: t.attendance, value: 'attendance' },
        { label: t.salary, value: 'salary' },
        { label: t.recruitment, value: 'recruitment' },
        { label: t.turnover, value: 'turnover' },
        { label: t.leave, value: 'leave' },
        { label: t.training, value: 'training' }
      ]
    },
    {
      key: 'status',
      label: t.status,
      options: [
        // CRITICAL FIX: Match backend Report model STATUS_CHOICES exactly
        { label: 'في الانتظار', value: 'pending' },    // Pending
        { label: 'قيد التشغيل', value: 'running' },     // Running
        { label: 'مكتمل', value: 'completed' },        // Completed
        { label: 'فشل', value: 'failed' }             // Failed
      ]
    },
    {
      key: 'format',
      label: t.format,
      options: [
        { label: t.pdf, value: 'pdf' },
        { label: t.excel, value: 'excel' },
        { label: t.csv, value: 'csv' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'name',
      label: t.name,
      type: 'text',
      required: true
    },
    {
      name: 'nameAr',
      label: t.name + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'type',
      label: t.type,
      type: 'select',
      required: true,
      options: [
        { label: t.employee, value: 'employee' },
        { label: t.performance, value: 'performance' },
        { label: t.attendance, value: 'attendance' },
        { label: t.salary, value: 'salary' },
        { label: t.recruitment, value: 'recruitment' },
        { label: t.turnover, value: 'turnover' },
        { label: t.leave, value: 'leave' },
        { label: t.training, value: 'training' }
      ]
    },
    {
      name: 'description',
      label: t.description,
      type: 'textarea',
      required: true
    },
    {
      name: 'descriptionAr',
      label: t.description + ' (عربي)',
      type: 'textarea',
      required: true
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        // CRITICAL FIX: Match backend Report model STATUS_CHOICES exactly
        { label: 'في الانتظار', value: 'pending' },    // Pending
        { label: 'قيد التشغيل', value: 'running' },     // Running
        { label: 'مكتمل', value: 'completed' },        // Completed
        { label: 'فشل', value: 'failed' }             // Failed
      ]
    },
    {
      name: 'lastGenerated',
      label: t.lastGenerated,
      type: 'date'
    },
    {
      name: 'size',
      label: t.size,
      type: 'text'
    },
    {
      name: 'downloads',
      label: t.downloads,
      type: 'number',
      min: 0
    },
    {
      name: 'createdBy',
      label: t.createdBy,
      type: 'text',
      required: true
    },
    {
      name: 'createdByAr',
      label: t.createdBy + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'reportPeriod',
      label: t.reportPeriod,
      type: 'text',
      required: true
    },
    {
      name: 'format',
      label: t.format,
      type: 'select',
      required: true,
      options: [
        { label: t.pdf, value: 'pdf' },
        { label: t.excel, value: 'excel' },
        { label: t.csv, value: 'csv' }
      ]
    },
    {
      name: 'isScheduled',
      label: t.isScheduled,
      type: 'checkbox'
    },
    {
      name: 'scheduleFrequency',
      label: t.scheduleFrequency,
      type: 'select',
      options: [
        { label: t.daily, value: 'daily' },
        { label: t.weekly, value: 'weekly' },
        { label: t.monthly, value: 'monthly' },
        { label: t.quarterly, value: 'quarterly' },
        { label: t.yearly, value: 'yearly' }
      ]
    }
  ]

  // Event handlers
  const handleCreate = (): void => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = (): void => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<HRReport>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('csv')
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  // Download report handler
  const handleDownloadReport = async (report: HRReport) => {
    try {
      // setLoading(true) // Loading handled by useCrud

      // Generate download URL based on report format
      const downloadUrl = `/api/hr-reports/${report.id}/download/`

      const response = await fetch(downloadUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })

      if (response.ok) {
        // Create blob and download
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url

        // Set filename based on report name and format
        const filename = `${report.name || 'hr-report'}.${report.format || 'pdf'}`
        link.download = filename

        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        // Update download count
        await updateItem(report.id, {
          downloads: (report.downloads || 0) + 1
        })

        toast.success(
          language === 'ar'
            ? `تم تحميل التقرير ${report.name} بنجاح`
            : `Report ${report.name} downloaded successfully`
        )
      } else {
        throw new Error('Failed to download report')
      }
    } catch (error) {
      console.error('Download report error:', error)
      toast.error(
        language === 'ar'
          ? 'فشل في تحميل التقرير'
          : 'Failed to download report'
      )
    } finally {
      // setLoading(false) // Loading handled by useCrud
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.hrReports}
        data={reports}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addReport : modalMode === 'edit' ? t.editReport : t.view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
