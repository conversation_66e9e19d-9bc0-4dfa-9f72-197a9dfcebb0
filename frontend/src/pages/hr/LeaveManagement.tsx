/**
 * Leave Management Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState, useEffect } from 'react'
import { useSelector } from 'react-redux'
import type { RootState } from '@/store'
import { Badge } from '@/components/ui/badge'
import {
  Calendar,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  User,
  CalendarDays,
  Eye,
  Edit,
  Trash2
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { leaveManagementService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface LeaveManagementProps {
  language: 'ar' | 'en'
}

interface LeaveRequest {
  id: number
  employee: number
  employee_name: string
  leave_type: number
  leave_type_name: string
  start_date: string
  end_date: string
  days_requested: number
  status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'CANCELLED'
  reason: string
  reason_ar: string
  approved_by?: number
  approved_by_name?: string
  approval_date?: string
  rejection_reason?: string
  rejection_reason_ar?: string
  created_at: string
  updated_at: string
}

const translations = {
  ar: {
    leaveManagement: 'إدارة الإجازات',
    addRequest: 'إضافة طلب إجازة',
    editRequest: 'تعديل طلب الإجازة',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف طلب الإجازة هذا؟',
    searchPlaceholder: 'البحث في طلبات الإجازات...',
    employee: 'الموظف',
    leaveType: 'نوع الإجازة',
    startDate: 'تاريخ البداية',
    endDate: 'تاريخ النهاية',
    duration: 'المدة (بالأيام)',
    status: 'الحالة',
    reason: 'السبب',
    appliedDate: 'تاريخ التقديم',
    approvedBy: 'معتمد بواسطة',
    approvedDate: 'تاريخ الاعتماد',
    comments: 'التعليقات',
    pending: 'معلق',
    approved: 'معتمد',
    rejected: 'مرفوض',
    employeeName: 'اسم الموظف',
    leaveTypes: {
      annual: 'إجازة سنوية',
      sick: 'إجازة مرضية',
      personal: 'إجازة شخصية',
      maternity: 'إجازة أمومة',
      emergency: 'إجازة طارئة'
    }
  },
  en: {
    leaveManagement: 'Leave Management',
    addRequest: 'Add Leave Request',
    editRequest: 'Edit Leave Request',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this leave request?',
    searchPlaceholder: 'Search leave requests...',
    employee: 'Employee',
    leaveType: 'Leave Type',
    startDate: 'Start Date',
    endDate: 'End Date',
    duration: 'Duration (days)',
    status: 'Status',
    reason: 'Reason',
    appliedDate: 'Applied Date',
    approvedBy: 'Approved By',
    approvedDate: 'Approved Date',
    comments: 'Comments',
    pending: 'Pending',
    approved: 'Approved',
    rejected: 'Rejected',
    employeeName: 'Employee Name',
    leaveTypes: {
      annual: 'Annual Leave',
      sick: 'Sick Leave',
      personal: 'Personal Leave',
      maternity: 'Maternity Leave',
      emergency: 'Emergency Leave'
    }
  }
}

export default function LeaveManagement({ language }: LeaveManagementProps): React.ReactElement {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')
  const [employees, setEmployees] = useState<Array<{id: string, name: string, user_id: string}>>([])

  // Get current user from Redux store
  const { user } = useSelector((state: RootState) => state.auth)

  const t = translations[language]
  const isRTL = language === 'ar'

  // Function to translate leave types
  const translateLeaveType = (leaveTypeName: string): void => {
    const typeMap: Record<string, string> = {
      'Annual Leave': language === 'ar' ? 'إجازة سنوية' : 'Annual Leave',
      'Sick Leave': language === 'ar' ? 'إجازة مرضية' : 'Sick Leave',
      'Emergency Leave': language === 'ar' ? 'إجازة طارئة' : 'Emergency Leave',
      'Maternity Leave': language === 'ar' ? 'إجازة أمومة' : 'Maternity Leave',
      'Study Leave': language === 'ar' ? 'إجازة دراسية' : 'Study Leave',
      'Personal Leave': language === 'ar' ? 'إجازة شخصية' : 'Personal Leave'
    }
    return typeMap[leaveTypeName] || leaveTypeName
  }

  // Fetch employees for dropdown
  const fetchEmployees = async () => {
    try {
      const { apiClient } = await import('../../services/api')
      const response = await apiClient.get<{
        count: number
        results: Array<{
          id: string
          employee_id: string
          user: {
            id: string
            first_name: string
            last_name: string
            get_full_name?: string
          }
        }>
      }>('/employees/')

      const employeeOptions = response.data.results.map(emp => ({
        id: emp.id,
        name: emp.user.get_full_name || `${emp.user.first_name} ${emp.user.last_name}`.trim() || emp.employee_id,
        user_id: emp.user.id
      }))

      setEmployees(employeeOptions)
    } catch (error) {
      console.error('Error fetching employees:', error)
    }
  }

  // Load employees on component mount
  useEffect(() => {
    fetchEmployees()
  }, [])

  // Use the generic CRUD hook
  const {
    items: leaveRequests,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<LeaveRequest>({
    service: leaveManagementService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const calculateDuration = (startDate: string, endDate: string): number => {
    const start = new Date(startDate)
    const end = new Date(endDate)
    const diffTime = Math.abs(end.getTime() - start.getTime())
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  }

  const getStatusIcon = (status: string): void => {
    const lowerStatus = status?.toLowerCase()
    switch (lowerStatus) {
      case 'pending':
        return <AlertCircle className="h-4 w-4 text-yellow-400" />
      case 'approved':
        return <CheckCircle className="h-4 w-4 text-green-400" />
      case 'rejected':
      case 'cancelled':
        return <XCircle className="h-4 w-4 text-red-400" />
      default:
        return <Clock className="h-4 w-4 text-gray-400" />
    }
  }

  const getStatusColor = (status: string): void => {
    const lowerStatus = status?.toLowerCase()
    switch (lowerStatus) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'approved':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'rejected':
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusLabel = (status: string): void => {
    const lowerStatus = status?.toLowerCase()
    switch (lowerStatus) {
      case 'pending':
        return language === 'ar' ? 'قيد الانتظار' : 'Pending'
      case 'approved':
        return language === 'ar' ? 'موافق عليه' : 'Approved'
      case 'rejected':
        return language === 'ar' ? 'مرفوض' : 'Rejected'
      case 'cancelled':
        return language === 'ar' ? 'ملغي' : 'Cancelled'
      default:
        return status || 'Unknown'
    }
  }

  // Table columns configuration
  const columns: TableColumn<LeaveRequest>[] = [
    {
      key: 'employee_name',
      label: t.employee,
      sortable: true,
      render: (item: LeaveRequest) => (
        <div className="flex items-center gap-2">
          <User className="h-4 w-4 text-blue-400" />
          <span className="text-white">
            {item.employee_name || 'N/A'}
          </span>
        </div>
      )
    },
    {
      key: 'leave_type_name',
      label: t.leaveType,
      render: (item: LeaveRequest) => (
        <span className="text-white/80">
          {translateLeaveType(item.leave_type_name) || 'N/A'}
        </span>
      )
    },
    {
      key: 'start_date',
      label: t.startDate,
      sortable: true,
      render: (item: LeaveRequest) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-green-400" />
          <span className="text-white/80">{item.start_date}</span>
        </div>
      )
    },
    {
      key: 'end_date',
      label: t.endDate,
      sortable: true,
      render: (item: LeaveRequest) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-red-400" />
          <span className="text-white/80">{item.end_date}</span>
        </div>
      )
    },
    {
      key: 'days_requested',
      label: t.duration,
      sortable: true,
      render: (item: LeaveRequest) => (
        <div className="flex items-center gap-1">
          <CalendarDays className="h-3 w-3 text-purple-400" />
          <span className="text-white font-medium">{item.days_requested} {language === 'ar' ? 'أيام' : 'days'}</span>
        </div>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: LeaveRequest) => (
        <div className="flex items-center gap-2">
          {getStatusIcon(item.status)}
          <Badge className={getStatusColor(item.status)}>
            {getStatusLabel(item.status)}
          </Badge>
        </div>
      )
    },
    {
      key: 'created_at',
      label: t.appliedDate,
      sortable: true,
      render: (item: LeaveRequest) => (
        <span className="text-white/80">
          {item.created_at ? new Date(item.created_at).toLocaleDateString() : 'N/A'}
        </span>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction<LeaveRequest>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: LeaveRequest) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: LeaveRequest) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: LeaveRequest) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.pending, value: 'pending' },
        { label: t.approved, value: 'approved' },
        { label: t.rejected, value: 'rejected' }
      ]
    },
    {
      key: 'leave_type',
      label: t.leaveType,
      options: [
        { label: t.leaveTypes.annual, value: 'annual' },
        { label: t.leaveTypes.sick, value: 'sick' },
        { label: t.leaveTypes.personal, value: 'personal' },
        { label: t.leaveTypes.maternity, value: 'maternity' },
        { label: t.leaveTypes.emergency, value: 'emergency' }
      ]
    }
  ]

  // Get current user role from auth context
  const currentUserRole = user?.role?.id || 'employee'
  const isHRManager = currentUserRole === 'hr_manager' || currentUserRole === 'admin' || currentUserRole === 'super_admin'
  const isEmployee = currentUserRole === 'employee'

  // Role-based form fields configuration
  const getFormFields = (): FormField[] => {
    const baseFields: FormField[] = []

    // Employee field logic:
    // - HR Manager: Can select any employee (for administrative purposes)
    // - Employee: Auto-filled with their own info (hidden field)
    if (isHRManager) {
      baseFields.push({
        name: 'employee',
        label: t.employeeName,
        type: 'select',
        required: true,
        options: employees.map(emp => ({
          label: emp.name,
          value: emp.id
        }))
      })
    }
    // For employees, we'll auto-fill this in handleSave
    // Leave type (dropdown for all users)
    baseFields.push({
      name: 'leave_type',
      label: t.leaveType,
      type: 'select',
      required: true,
      options: [
        { label: t.leaveTypes.annual, value: 'annual' },
        { label: t.leaveTypes.sick, value: 'sick' },
        { label: t.leaveTypes.personal, value: 'personal' },
        { label: t.leaveTypes.maternity, value: 'maternity' },
        { label: t.leaveTypes.emergency, value: 'emergency' }
      ]
    })
    // Date fields (for all users)
    baseFields.push(
      {
        name: 'start_date',
        label: t.startDate,
        type: 'date',
        required: true
      },
      {
        name: 'end_date',
        label: t.endDate,
        type: 'date',
        required: true
      },
      {
        name: 'duration',
        label: t.duration,
        type: 'number',
        required: true,
        min: 1
      },
      {
        name: 'reason',
        label: t.reason,
        type: 'textarea',
        required: true
      }
    )
    // Status field logic:
    // - HR Manager: Can set any status (for reviewing/managing requests)
    // - Employee: Always 'pending' (auto-set, not shown in form)
    if (isHRManager) {
      baseFields.push({
        name: 'status',
        label: t.status,
        type: 'select',
        required: true,
        options: [
          { label: t.pending, value: 'pending' },
          { label: t.approved, value: 'approved' },
          { label: t.rejected, value: 'rejected' }
        ]
      })
    }
    // For employees, status is auto-set to 'pending' in handleSave

    // Applied date logic:
    // - HR Manager: Can edit (for administrative corrections)
    // - Employee: Auto-set to today (not shown in form)
    if (isHRManager) {
      baseFields.push({
        name: 'applied_date',
        label: t.appliedDate,
        type: 'date',
        required: true
      })
    }
    // For employees, applied_date is auto-set to today in handleSave

    // Approval fields (HR Manager only)
    if (isHRManager) {
      baseFields.push(
        {
          name: 'approved_by',
          label: t.approvedBy,
          type: 'text'
        },
        {
          name: 'approved_date',
          label: t.approvedDate,
          type: 'date'
        },
        {
          name: 'comments',
          label: t.comments,
          type: 'textarea'
        }
      )
    }

    return baseFields
  }

  // Get form fields based on role
  const formFields: FormField[] = getFormFields()

  // Event handlers
  const handleCreate = (): void => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = (): void => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<LeaveRequest>) => {
    try {
      // Transform data based on role
      const transformedData = { ...data }

      // For employees: auto-set required fields
      if (isEmployee) {
        // Auto-set status to pending (employees can't approve their own requests)
        transformedData.status = 'pending'

        // Auto-set applied date to today
        transformedData.applied_date = new Date().toISOString().split('T')[0]

        // Auto-set employee to current user's employee record
        // TODO: Get current user's employee ID from auth context
        // transformedData.employee = currentUser.employee_id

        // Clear approval fields (employees can't set these)
        delete transformedData.approved_by
        delete transformedData.approved_date
        delete transformedData.comments
      }

      // For HR Manager: ensure proper data handling
      if (isHRManager && transformedData.employee) {
        // The employee field contains the employee ID from the dropdown
        // Remove any old employee_name fields if they exist
        delete transformedData.employee_name
        delete transformedData.employee_nameAr
      }

      if (modalMode === 'create') {
        await createItem(transformedData)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, transformedData)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async (format: 'csv' | 'excel' | 'pdf' = 'csv') => {
    try {
      if (format === 'pdf') {
        // Use new PDF generation API for leave reports
        const response = await fetch(`http://localhost:8000/api/pdf/generate/hr-report/?language=${language}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('auth_token') || ''}`,
          }
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const pdfBlob = await response.blob()
        const url = window.URL.createObjectURL(pdfBlob)
        const link = document.createElement('a')
        link.href = url
        link.download = `leave-management-report-${language}-${new Date().toISOString().split('T')[0]}.pdf`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
      } else {
        await exportData('csv')
      }
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>

      {/* CRUD Table */}
      <CrudTable
        title={t.leaveManagement}
        data={leaveRequests}
        columns={columns}
        actions={actions}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
        createButtonText={t.addRequest}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addRequest : modalMode === 'edit' ? t.editRequest : t.view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
