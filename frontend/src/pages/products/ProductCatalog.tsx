/**
 * Product Catalog Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  Package,
  Star,
  AlertTriangle,
  CheckCircle,
  DollarSign,
  ShoppingCart,
  Eye,
  Edit,
  Trash2,
  Barcode,
  Tag,
  TrendingUp,
  Building
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { productService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface ProductCatalogProps {
  language: 'ar' | 'en'
}

interface Product {
  id: number
  name: string
  nameAr: string
  description: string
  descriptionAr: string
  sku: string
  category: string
  categoryAr: string
  brand: string
  brandAr: string
  price: number
  costPrice: number
  stockQuantity: number
  minStockLevel: number
  status: 'active' | 'inactive' | 'discontinued' | 'out-of-stock'
  rating: number
  reviewsCount: number
  salesCount: number
  imageUrl?: string
  tags: string[]
  tagsAr: string[]
  weight?: number
  dimensions?: {
    length: number
    width: number
    height: number
  }
  supplier: string
  supplierAr: string
  createdDate: string
  lastUpdated: string
}

const translations = {
  ar: {
    productCatalog: 'كتالوج المنتجات',
    addProduct: 'إضافة منتج',
    editProduct: 'تعديل المنتج',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذا المنتج؟',
    searchPlaceholder: 'البحث في المنتجات...',
    name: 'اسم المنتج',
    description: 'الوصف',
    sku: 'رمز المنتج',
    category: 'الفئة',
    brand: 'العلامة التجارية',
    price: 'السعر',
    costPrice: 'سعر التكلفة',
    stockQuantity: 'كمية المخزون',
    minStockLevel: 'الحد الأدنى للمخزون',
    status: 'الحالة',
    rating: 'التقييم',
    reviewsCount: 'عدد المراجعات',
    salesCount: 'عدد المبيعات',
    imageUrl: 'رابط الصورة',
    tags: 'العلامات',
    weight: 'الوزن',
    dimensions: 'الأبعاد',
    supplier: 'المورد',
    createdDate: 'تاريخ الإنشاء',
    lastUpdated: 'آخر تحديث',
    active: 'نشط',
    inactive: 'غير نشط',
    discontinued: 'متوقف',
    'out-of-stock': 'نفد المخزون',
    lowStock: 'مخزون منخفض',
    inStock: 'متوفر',
    categories: {
      electronics: 'الإلكترونيات',
      clothing: 'الملابس',
      books: 'الكتب',
      home: 'المنزل',
      sports: 'الرياضة',
      beauty: 'الجمال'
    }
  },
  en: {
    productCatalog: 'Product Catalog',
    addProduct: 'Add Product',
    editProduct: 'Edit Product',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this product?',
    searchPlaceholder: 'Search products...',
    name: 'Product Name',
    description: 'Description',
    sku: 'SKU',
    category: 'Category',
    brand: 'Brand',
    price: 'Price',
    costPrice: 'Cost Price',
    stockQuantity: 'Stock Quantity',
    minStockLevel: 'Min Stock Level',
    status: 'Status',
    rating: 'Rating',
    reviewsCount: 'Reviews Count',
    salesCount: 'Sales Count',
    imageUrl: 'Image URL',
    tags: 'Tags',
    weight: 'Weight',
    dimensions: 'Dimensions',
    supplier: 'Supplier',
    createdDate: 'Created Date',
    lastUpdated: 'Last Updated',
    active: 'Active',
    inactive: 'Inactive',
    discontinued: 'Discontinued',
    'out-of-stock': 'Out of Stock',
    lowStock: 'Low Stock',
    inStock: 'In Stock',
    categories: {
      electronics: 'Electronics',
      clothing: 'Clothing',
      books: 'Books',
      home: 'Home & Garden',
      sports: 'Sports',
      beauty: 'Beauty & Health'
    }
  }
}

export default function ProductCatalog({ language }: ProductCatalogProps): void {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: products,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<Product>({
    service: productService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getStatusColor = (status: string): void => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 border-green-200'
      case 'inactive': return 'bg-gray-100 text-gray-800 border-gray-200'
      case 'discontinued': return 'bg-red-100 text-red-800 border-red-200'
      case 'out-of-stock': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStockStatus = (stockQuantity: number, minStockLevel: number): void => {
    if (stockQuantity === 0) return 'out-of-stock'
    if (stockQuantity <= minStockLevel) return 'lowStock'
    return 'inStock'
  }

  const getStockColor = (stockQuantity: number, minStockLevel: number): void => {
    const status = getStockStatus(stockQuantity, minStockLevel)
    switch (status) {
      case 'out-of-stock': return 'text-red-400'
      case 'lowStock': return 'text-yellow-400'
      case 'inStock': return 'text-green-400'
      default: return 'text-gray-400'
    }
  }

  const getStockIcon = (stockQuantity: number, minStockLevel: number): void => {
    const status = getStockStatus(stockQuantity, minStockLevel)
    switch (status) {
      case 'out-of-stock': return <AlertTriangle className="h-3 w-3 text-red-400" />
      case 'lowStock': return <AlertTriangle className="h-3 w-3 text-yellow-400" />
      case 'inStock': return <CheckCircle className="h-3 w-3 text-green-400" />
      default: return <Package className="h-3 w-3 text-gray-400" />
    }
  }

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount)
  }

  const getRatingStars = (rating: number): void => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-3 w-3 ${
          i < rating ? 'text-yellow-400 fill-current' : 'text-gray-400'
        }`}
      />
    ))
  }
  // Table columns configuration
  const columns: TableColumn<Product>[] = [
    {
      key: 'name',
      label: t.name,
      sortable: true,
      render: (item: Product) => (
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 rounded-lg bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-white">
            {item.imageUrl ? (
              <img src={item.imageUrl} alt={item.name} className="w-8 h-8 rounded object-cover" />
            ) : (
              <Package className="h-4 w-4" />
            )}
          </div>
          <div>
            <div className="font-medium text-white">
              {language === 'ar' ? item.nameAr : item.name}
            </div>
            <div className="text-sm text-white/60">
              {language === 'ar' ? item.brandAr : item.brand}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'sku',
      label: t.sku,
      sortable: true,
      render: (item: Product) => (
        <div className="flex items-center gap-1">
          <Barcode className="h-3 w-3 text-blue-400" />
          <span className="text-white font-mono">{item.sku}</span>
        </div>
      )
    },
    {
      key: 'category',
      label: t.category,
      sortable: true,
      render: (item: Product) => (
        <div className="flex items-center gap-1">
          <Tag className="h-3 w-3 text-purple-400" />
          <span className="text-white/80">
            {language === 'ar' ? item.categoryAr : item.category}
          </span>
        </div>
      )
    },
    {
      key: 'price',
      label: t.price,
      sortable: true,
      render: (item: Product) => (
        <div className="flex items-center gap-1">
          <DollarSign className="h-3 w-3 text-green-400" />
          <span className="text-green-400 font-medium">
            {formatCurrency(item.price)}
          </span>
        </div>
      )
    },
    {
      key: 'stockQuantity',
      label: t.stockQuantity,
      sortable: true,
      render: (item: Product) => (
        <div className="flex items-center gap-2">
          {getStockIcon(item.stockQuantity, item.minStockLevel)}
          <div>
            <span className={`font-medium ${getStockColor(item.stockQuantity, item.minStockLevel)}`}>
              {item.stockQuantity}
            </span>
            {item.stockQuantity <= item.minStockLevel && (
              <div className="text-xs text-yellow-400">{t.lowStock}</div>
            )}
          </div>
        </div>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: Product) => (
        <Badge className={getStatusColor(item.status)}>
          {String(t[item.status as keyof typeof t])}
        </Badge>
      )
    },
    {
      key: 'rating',
      label: t.rating,
      sortable: true,
      render: (item: Product) => (
        <div className="flex items-center gap-2">
          <div className="flex">
            {getRatingStars(item.rating)}
          </div>
          <span className="text-white text-sm">{item.rating}</span>
          <span className="text-white/50 text-xs">({item.reviewsCount})</span>
        </div>
      )
    },
    {
      key: 'salesCount',
      label: t.salesCount,
      sortable: true,
      render: (item: Product) => (
        <div className="flex items-center gap-1">
          <ShoppingCart className="h-3 w-3 text-orange-400" />
          <span className="text-white/80">{item.salesCount}</span>
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction<Product>[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: (item) => {
        if (window.confirm(t.confirmDelete)) {
          deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300'
    }
  ]

  // Filter options configuration
  const filterOptions: FilterOption[] = [
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.active, value: 'active' },
        { label: t.inactive, value: 'inactive' },
        { label: t.discontinued, value: 'discontinued' },
        { label: t['out-of-stock'], value: 'out-of-stock' }
      ]
    },
    {
      key: 'category',
      label: t.category,
      options: [
        { label: t.categories.electronics, value: 'electronics' },
        { label: t.categories.clothing, value: 'clothing' },
        { label: t.categories.books, value: 'books' },
        { label: t.categories.home, value: 'home' },
        { label: t.categories.sports, value: 'sports' },
        { label: t.categories.beauty, value: 'beauty' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'name',
      label: t.name,
      type: 'text',
      required: true,
      placeholder: language === 'ar' ? 'أدخل اسم المنتج' : 'Enter product name'
    },
    {
      name: 'nameAr',
      label: t.name + ' (عربي)',
      type: 'text',
      required: true,
      placeholder: 'أدخل اسم المنتج بالعربية'
    },
    {
      name: 'description',
      label: t.description,
      type: 'textarea',
      required: true,
      placeholder: language === 'ar' ? 'أدخل وصف المنتج' : 'Enter product description'
    },
    {
      name: 'descriptionAr',
      label: t.description + ' (عربي)',
      type: 'textarea',
      required: true,
      placeholder: 'أدخل وصف المنتج بالعربية'
    },
    {
      name: 'sku',
      label: t.sku,
      type: 'text',
      required: true,
      placeholder: 'PRD-001'
    },
    {
      name: 'category',
      label: t.category,
      type: 'select',
      required: true,
      options: [
        { label: t.categories.electronics, value: 'electronics' },
        { label: t.categories.clothing, value: 'clothing' },
        { label: t.categories.books, value: 'books' },
        { label: t.categories.home, value: 'home' },
        { label: t.categories.sports, value: 'sports' },
        { label: t.categories.beauty, value: 'beauty' }
      ]
    },
    {
      name: 'categoryAr',
      label: t.category + ' (عربي)',
      type: 'select',
      required: true,
      options: [
        { label: 'الإلكترونيات', value: 'الإلكترونيات' },
        { label: 'الملابس', value: 'الملابس' },
        { label: 'الكتب', value: 'الكتب' },
        { label: 'المنزل', value: 'المنزل' },
        { label: 'الرياضة', value: 'الرياضة' },
        { label: 'الجمال', value: 'الجمال' }
      ]
    },
    {
      name: 'brand',
      label: t.brand,
      type: 'text',
      required: true,
      placeholder: language === 'ar' ? 'أدخل العلامة التجارية' : 'Enter brand name'
    },
    {
      name: 'brandAr',
      label: t.brand + ' (عربي)',
      type: 'text',
      required: true,
      placeholder: 'أدخل العلامة التجارية بالعربية'
    },
    {
      name: 'price',
      label: t.price,
      type: 'number',
      required: true,
      placeholder: '0.00'
    },
    {
      name: 'costPrice',
      label: t.costPrice,
      type: 'number',
      required: true,
      placeholder: '0.00'
    },
    {
      name: 'stockQuantity',
      label: t.stockQuantity,
      type: 'number',
      required: true,
      placeholder: '0'
    },
    {
      name: 'minStockLevel',
      label: t.minStockLevel,
      type: 'number',
      required: true,
      placeholder: '5'
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: t.active, value: 'active' },
        { label: t.inactive, value: 'inactive' },
        { label: t.discontinued, value: 'discontinued' },
        { label: t['out-of-stock'], value: 'out-of-stock' }
      ]
    },
    {
      name: 'supplier',
      label: t.supplier,
      type: 'text',
      required: true,
      placeholder: language === 'ar' ? 'أدخل اسم المورد' : 'Enter supplier name'
    },
    {
      name: 'supplierAr',
      label: t.supplier + ' (عربي)',
      type: 'text',
      required: true,
      placeholder: 'أدخل اسم المورد بالعربية'
    },
    {
      name: 'imageUrl',
      label: t.imageUrl,
      type: 'text',
      placeholder: 'https://example.com/image.jpg'
    },
    {
      name: 'weight',
      label: t.weight,
      type: 'number',
      placeholder: '0.0'
    }
  ]

  const handleCreate = (): void => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = (): void => {
    setShowModal(false)
    selectItem(null)
  }

  const handleSubmit = async (data: Partial<Product>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      handleModalClose()
    } catch (error) {
      console.error('Error submitting form:', error)
    }
  }

  return (
    <div className="space-y-6">
      {React.createElement(CrudTable as any,
        {
          data: products,
          columns: columns,
          actions: actions,
          loading: loading,
          searchQuery: searchQuery,
          onSearchChange: setSearchQuery,
          searchPlaceholder: t.searchPlaceholder,
          activeFilters: filters,
          onFiltersChange: setFilters,
          filterOptions: filterOptions,
          sortBy: sortBy,
          sortOrder: sortOrder,
          onSortChange: setSorting,
          onCreateNew: handleCreate,
          createButtonText: t.addProduct,
          title: t.productCatalog,
          language: language,
          onExport: exportData,
          onRefresh: refresh,
          error: error,
          onClearError: clearError
        }
      )}

      {React.createElement(CrudModal as any,
        {
          isOpen: showModal,
          onClose: handleModalClose,
          mode: modalMode,
          title: modalMode === 'create' ? t.addProduct : modalMode === 'edit' ? t.editProduct : t.view,
          fields: formFields,
          initialData: selectedItem,
          onSubmit: handleSubmit,
          loading: creating || updating,
          language: language
        }
      )}
    </div>
  )
}
