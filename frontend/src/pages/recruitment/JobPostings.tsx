/**
 * Job Postings Page with Full CRUD Implementation
 * Uses generic CRUD components for standardized functionality
 */

import React, { useState } from 'react'
import { Badge } from '@/components/ui/badge'
import {
  Briefcase,
  Eye,
  Edit,
  Trash2,
  MapPin,
  DollarSign,
  Users,
  Calendar,
  TrendingUp
} from 'lucide-react'
import { useCrud } from '@/hooks/useCrud'
import { jobPostingsService } from '@/services/crudService'
import CrudTable, { TableColumn, TableAction, FilterOption } from '@/components/common/CrudTable'
import CrudModal, { FormField } from '@/components/common/CrudModal'

interface JobPostingsProps {
  language: 'ar' | 'en'
}

interface JobPosting {
  id: number
  title: string
  titleAr: string
  department: string
  departmentAr: string
  location: string
  locationAr: string
  employmentType: 'full-time' | 'part-time' | 'contract' | 'internship'
  experienceLevel: 'entry' | 'mid' | 'senior' | 'executive'
  salaryMin: number
  salaryMax: number
  status: 'draft' | 'active' | 'paused' | 'closed'
  postedDate: string
  closingDate: string
  applicationsCount: number
  viewsCount: number
  description: string
  descriptionAr: string
  requirements: string
  requirementsAr: string
  benefits: string
  benefitsAr: string
  isRemote: boolean
  isUrgent: boolean
}

const translations = {
  ar: {
    jobPostings: 'الوظائف المعلنة',
    addJob: 'إضافة وظيفة',
    editJob: 'تعديل الوظيفة',
    view: 'عرض',
    edit: 'تعديل',
    delete: 'حذف',
    confirmDelete: 'هل أنت متأكد من حذف هذه الوظيفة؟',
    searchPlaceholder: 'البحث في الوظائف المعلنة...',
    title: 'المسمى الوظيفي',
    department: 'القسم',
    location: 'الموقع',
    employmentType: 'نوع التوظيف',
    experienceLevel: 'مستوى الخبرة',
    salaryMin: 'الحد الأدنى للراتب',
    salaryMax: 'الحد الأقصى للراتب',
    status: 'الحالة',
    postedDate: 'تاريخ النشر',
    closingDate: 'تاريخ الإغلاق',
    applicationsCount: 'عدد الطلبات',
    viewsCount: 'عدد المشاهدات',
    description: 'الوصف',
    requirements: 'المتطلبات',
    benefits: 'المزايا',
    isRemote: 'عمل عن بعد',
    isUrgent: 'عاجل',
    draft: 'مسودة',
    active: 'نشط',
    paused: 'متوقف',
    closed: 'مغلق',
    'full-time': 'دوام كامل',
    'part-time': 'دوام جزئي',
    contract: 'عقد',
    internship: 'تدريب',
    entry: 'مبتدئ',
    mid: 'متوسط',
    senior: 'أول',
    executive: 'تنفيذي'
  },
  en: {
    jobPostings: 'Job Postings',
    addJob: 'Add Job Posting',
    editJob: 'Edit Job Posting',
    view: 'View',
    edit: 'Edit',
    delete: 'Delete',
    confirmDelete: 'Are you sure you want to delete this job posting?',
    searchPlaceholder: 'Search job postings...',
    title: 'Job Title',
    department: 'Department',
    location: 'Location',
    employmentType: 'Employment Type',
    experienceLevel: 'Experience Level',
    salaryMin: 'Minimum Salary',
    salaryMax: 'Maximum Salary',
    status: 'Status',
    postedDate: 'Posted Date',
    closingDate: 'Closing Date',
    applicationsCount: 'Applications Count',
    viewsCount: 'Views Count',
    description: 'Description',
    requirements: 'Requirements',
    benefits: 'Benefits',
    isRemote: 'Remote Work',
    isUrgent: 'Urgent',
    draft: 'Draft',
    active: 'Active',
    paused: 'Paused',
    closed: 'Closed',
    'full-time': 'Full Time',
    'part-time': 'Part Time',
    contract: 'Contract',
    internship: 'Internship',
    entry: 'Entry',
    mid: 'Mid',
    senior: 'Senior',
    executive: 'Executive'
  }
}

export default function JobPostings({ language }: JobPostingsProps): void {
  const [showModal, setShowModal] = useState(false)
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('create')

  const t = translations[language]
  const isRTL = language === 'ar'

  // Use the generic CRUD hook
  const {
    items: jobPostings,
    selectedItem,
    loading,
    creating,
    updating,
    deleting,
    error,
    createItem,
    updateItem,
    deleteItem,
    selectItem,
    searchQuery,
    setSearchQuery,
    filters,
    setFilters,
    sortBy,
    sortOrder,
    setSorting,
    refresh,
    exportData,
    clearError
  } = useCrud<JobPosting>({
    service: jobPostingsService,
    autoLoad: true,
    pageSize: 20
  })

  // Helper functions
  const getStatusColor = (status: string): void => {
    switch (status) {
      case 'draft': return 'bg-gray-100 text-gray-800 border-gray-200'
      case 'active': return 'bg-green-100 text-green-800 border-green-200'
      case 'paused': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'closed': return 'bg-red-100 text-red-800 border-red-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getEmploymentTypeColor = (type: string): void => {
    switch (type) {
      case 'full-time': return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'part-time': return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'contract': return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'internship': return 'bg-green-100 text-green-800 border-green-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('ar-SA', {
      style: 'currency',
      currency: 'SAR'
    }).format(amount)
  }

  // Table columns configuration
  const columns: TableColumn<JobPosting>[] = [
    {
      key: 'title',
      label: t.title,
      sortable: true,
      render: (item: JobPosting) => (
        <div className="flex items-center gap-2">
          <Briefcase className="h-4 w-4 text-blue-400" />
          <div>
            <div className="font-medium text-white">
              {language === 'ar' ? item.titleAr : item.title}
            </div>
            <div className="flex items-center gap-2 mt-1">
              {item.isRemote && (
                <Badge className="text-xs bg-blue-500/20 text-blue-300 border-blue-500/30">
                  Remote
                </Badge>
              )}
              {item.isUrgent && (
                <Badge className="text-xs bg-red-500/20 text-red-300 border-red-500/30">
                  Urgent
                </Badge>
              )}
            </div>
          </div>
        </div>
      )
    },
    {
      key: 'department',
      label: t.department,
      sortable: true,
      render: (item: JobPosting) => (
        <span className="text-white/80">
          {language === 'ar' ? item.departmentAr : item.department}
        </span>
      )
    },
    {
      key: 'location',
      label: t.location,
      render: (item: JobPosting) => (
        <div className="flex items-center gap-1">
          <MapPin className="h-3 w-3 text-green-400" />
          <span className="text-white/80">
            {language === 'ar' ? item.locationAr : item.location}
          </span>
        </div>
      )
    },
    {
      key: 'employmentType',
      label: t.employmentType,
      sortable: true,
      render: (item: JobPosting) => (
        <Badge className={getEmploymentTypeColor(item.employmentType)}>
          {t[item.employmentType as keyof typeof t]}
        </Badge>
      )
    },
    {
      key: 'status',
      label: t.status,
      sortable: true,
      render: (item: JobPosting) => (
        <Badge className={getStatusColor(item.status)}>
          {t[item.status as keyof typeof t]}
        </Badge>
      )
    },
    {
      key: 'applicationsCount',
      label: t.applicationsCount,
      sortable: true,
      render: (item: JobPosting) => (
        <div className="flex items-center gap-2">
          <Users className="h-3 w-3 text-purple-400" />
          <span className="text-white font-medium">{item.applicationsCount}</span>
          <span className="text-white/50 text-sm">({item.viewsCount} views)</span>
        </div>
      )
    },
    {
      key: 'salary',
      label: 'Salary Range',
      sortable: true,
      render: (item: JobPosting) => (
        <div className="flex items-center gap-1">
          <DollarSign className="h-3 w-3 text-green-400" />
          <span className="text-white font-medium">
            {formatCurrency(item.salaryMin)} - {formatCurrency(item.salaryMax)}
          </span>
        </div>
      )
    },
    {
      key: 'postedDate',
      label: t.postedDate,
      sortable: true,
      render: (item: JobPosting) => (
        <div className="flex items-center gap-1">
          <Calendar className="h-3 w-3 text-blue-400" />
          <span className="text-white/80">{item.postedDate}</span>
        </div>
      )
    }
  ]

  // Table actions configuration
  const actions: TableAction[] = [
    {
      label: t.view,
      icon: Eye,
      onClick: (item: React.MouseEvent) => {
        selectItem(item)
        setModalMode('view')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.edit,
      icon: Edit,
      onClick: (item: React.MouseEvent) => {
        selectItem(item)
        setModalMode('edit')
        setShowModal(true)
      },
      variant: 'ghost'
    },
    {
      label: t.delete,
      icon: Trash2,
      onClick: async (item: React.MouseEvent) => {
        if (window.confirm(t.confirmDelete)) {
          await deleteItem(item.id)
        }
      },
      variant: 'ghost',
      className: 'text-red-400 hover:text-red-300 hover:bg-red-500/20'
    }
  ]

  // Filter options
  const filterOptions: FilterOption[] = [
    {
      key: 'status',
      label: t.status,
      options: [
        { label: t.draft, value: 'draft' },
        { label: t.active, value: 'active' },
        { label: t.paused, value: 'paused' },
        { label: t.closed, value: 'closed' }
      ]
    },
    {
      key: 'employmentType',
      label: t.employmentType,
      options: [
        { label: t['full-time'], value: 'full-time' },
        { label: t['part-time'], value: 'part-time' },
        { label: t.contract, value: 'contract' },
        { label: t.internship, value: 'internship' }
      ]
    },
    {
      key: 'experienceLevel',
      label: t.experienceLevel,
      options: [
        { label: t.entry, value: 'entry' },
        { label: t.mid, value: 'mid' },
        { label: t.senior, value: 'senior' },
        { label: t.executive, value: 'executive' }
      ]
    }
  ]

  // Form fields configuration
  const formFields: FormField[] = [
    {
      name: 'title',
      label: t.title,
      type: 'text',
      required: true
    },
    {
      name: 'titleAr',
      label: t.title + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'department',
      label: t.department,
      type: 'text',
      required: true
    },
    {
      name: 'departmentAr',
      label: t.department + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'location',
      label: t.location,
      type: 'text',
      required: true
    },
    {
      name: 'locationAr',
      label: t.location + ' (عربي)',
      type: 'text',
      required: true
    },
    {
      name: 'employmentType',
      label: t.employmentType,
      type: 'select',
      required: true,
      options: [
        { label: t['full-time'], value: 'full-time' },
        { label: t['part-time'], value: 'part-time' },
        { label: t.contract, value: 'contract' },
        { label: t.internship, value: 'internship' }
      ]
    },
    {
      name: 'experienceLevel',
      label: t.experienceLevel,
      type: 'select',
      required: true,
      options: [
        { label: t.entry, value: 'entry' },
        { label: t.mid, value: 'mid' },
        { label: t.senior, value: 'senior' },
        { label: t.executive, value: 'executive' }
      ]
    },
    {
      name: 'salaryMin',
      label: t.salaryMin,
      type: 'number',
      required: true,
      min: 0
    },
    {
      name: 'salaryMax',
      label: t.salaryMax,
      type: 'number',
      required: true,
      min: 0
    },
    {
      name: 'status',
      label: t.status,
      type: 'select',
      required: true,
      options: [
        { label: t.draft, value: 'draft' },
        { label: t.active, value: 'active' },
        { label: t.paused, value: 'paused' },
        { label: t.closed, value: 'closed' }
      ]
    },
    {
      name: 'postedDate',
      label: t.postedDate,
      type: 'date',
      required: true
    },
    {
      name: 'closingDate',
      label: t.closingDate,
      type: 'date',
      required: true
    },
    {
      name: 'applicationsCount',
      label: t.applicationsCount,
      type: 'number',
      min: 0
    },
    {
      name: 'viewsCount',
      label: t.viewsCount,
      type: 'number',
      min: 0
    },
    {
      name: 'description',
      label: t.description,
      type: 'textarea',
      required: true
    },
    {
      name: 'descriptionAr',
      label: t.description + ' (عربي)',
      type: 'textarea',
      required: true
    },
    {
      name: 'requirements',
      label: t.requirements,
      type: 'textarea',
      placeholder: 'One requirement per line'
    },
    {
      name: 'requirementsAr',
      label: t.requirements + ' (عربي)',
      type: 'textarea',
      placeholder: 'متطلب واحد في كل سطر'
    },
    {
      name: 'benefits',
      label: t.benefits,
      type: 'textarea',
      placeholder: 'One benefit per line'
    },
    {
      name: 'benefitsAr',
      label: t.benefits + ' (عربي)',
      type: 'textarea',
      placeholder: 'ميزة واحدة في كل سطر'
    },
    {
      name: 'isRemote',
      label: t.isRemote,
      type: 'checkbox'
    },
    {
      name: 'isUrgent',
      label: t.isUrgent,
      type: 'checkbox'
    }
  ]

  // Event handlers
  const handleCreate = (): void => {
    setModalMode('create')
    setShowModal(true)
  }

  const handleModalClose = (): void => {
    setShowModal(false)
    selectItem(null)
    clearError()
  }

  const handleSave = async (data: Partial<JobPosting>) => {
    try {
      if (modalMode === 'create') {
        await createItem(data)
      } else if (modalMode === 'edit' && selectedItem) {
        await updateItem(selectedItem.id, data)
      }
      setShowModal(false)
    } catch (error) {
      console.error('Save error:', error)
    }
  }

  const handleExport = async () => {
    try {
      await exportData('csv')
    } catch (error) {
      console.error('Export error:', error)
    }
  }

  return (
    <div className={`space-y-6 ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* CRUD Table */}
      <CrudTable
        title={t.jobPostings}
        data={jobPostings}
        columns={columns}
        actions={actions as any}
        filters={filterOptions}
        loading={loading}
        searchPlaceholder={t.searchPlaceholder}
        language={language}
        onCreate={handleCreate}
        onRefresh={refresh}
        onExport={handleExport}
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        activeFilters={filters as Record<string, string>}
        onFilterChange={setFilters}
        sortBy={sortBy}
        sortOrder={sortOrder}
        onSortChange={setSorting}
      />

      {/* CRUD Modal */}
      <CrudModal
        isOpen={showModal}
        onClose={handleModalClose}
        onSave={handleSave}
        title={modalMode === 'create' ? t.addJob : modalMode === 'edit' ? t.editJob : t.view}
        fields={formFields}
        initialData={selectedItem as any}
        language={language}
        loading={creating || updating}
      />
    </div>
  )
}
