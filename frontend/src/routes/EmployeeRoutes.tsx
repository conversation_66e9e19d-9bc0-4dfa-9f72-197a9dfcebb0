import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom'
import RoleBasedRoute from '../components/RoleBasedRoute'
import Unauthorized from '../pages/Unauthorized'

// Employee Dashboard
import EmployeeDashboard from '../pages/dashboards/EmployeeDashboard'

// Employee Pages (Employee-specific versions)
import EmployeeTasks from '../pages/employee-specific/EmployeeTasks'
import EmployeeProfile from '../pages/employee-specific/EmployeeProfile'
import EmployeeLeave from '../pages/employee-specific/EmployeeLeave'
import EmployeeAttendance from '../pages/employee-specific/EmployeeAttendance'
import EmployeePerformance from '../pages/employee-specific/EmployeePerformance'
import EmployeeProjects from '../pages/employee-specific/EmployeeProjects'

// Communication (Employee-restricted versions)
import Messages from '../pages/communication/Messages'
import EmployeeAnnouncements from '../pages/employee-specific/EmployeeAnnouncements'
import EmployeeDocuments from '../pages/employee-specific/EmployeeDocuments'
import Meetings from '../pages/communication/Meetings'

// Personal Features
import PersonalProfile from '../pages/personal/PersonalProfile'
import PersonalMessages from '../pages/personal/PersonalMessages'
import PersonalCalendar from '../pages/personal/PersonalCalendar'

interface EmployeeRoutesProps {
  language: 'ar' | 'en'
}

export default function EmployeeRoutes({ language }: EmployeeRoutesProps): void {
  return (
    <Routes>
      {/* Dashboard */}
      <Route path="/" element={
        <RoleBasedRoute requiredRole="employee" fallbackPath="/employee/unauthorized">
          <EmployeeDashboard language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/employee/dashboard" element={
        <RoleBasedRoute requiredRole="employee" fallbackPath="/employee/unauthorized">
          <EmployeeDashboard language={language} />
        </RoleBasedRoute>
      } />

      {/* Employee Self-Service */}
      <Route path="/employee/profile" element={
        <RoleBasedRoute requiredRole="employee" fallbackPath="/employee/unauthorized">
          <EmployeeProfile language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/employee/leave" element={
        <RoleBasedRoute requiredRole="employee" fallbackPath="/employee/unauthorized">
          <EmployeeLeave language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/employee/tasks" element={
        <EmployeeTasks language={language} />
      } />

      {/* Project Management (Employee-specific) */}
      <Route path="/employee/projects" element={
        <RoleBasedRoute requiredRole="employee" fallbackPath="/employee/unauthorized">
          <EmployeeProjects language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/employee/projects/tasks" element={
        <RoleBasedRoute requiredRole="employee" fallbackPath="/employee/unauthorized">
          <EmployeeTasks language={language} />
        </RoleBasedRoute>
      } />

      {/* Personal Features - Profile route already defined above */}
      <Route path="/employee/messages" element={
        <RoleBasedRoute requiredRole="employee" fallbackPath="/employee/unauthorized">
          <PersonalMessages language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/employee/calendar" element={
        <RoleBasedRoute requiredRole="employee" fallbackPath="/employee/unauthorized">
          <PersonalCalendar language={language} />
        </RoleBasedRoute>
      } />

      {/* Additional Employee Routes */}
      <Route path="/employee/attendance" element={
        <RoleBasedRoute requiredRole="employee" fallbackPath="/employee/unauthorized">
          <EmployeeAttendance language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/employee/performance" element={
        <RoleBasedRoute requiredRole="employee" fallbackPath="/employee/unauthorized">
          <EmployeePerformance language={language} />
        </RoleBasedRoute>
      } />

      {/* Communication */}
      <Route path="/employee/communication/messages" element={
        <RoleBasedRoute requiredRole="employee" fallbackPath="/employee/unauthorized">
          <Messages language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/employee/communication/announcements" element={
        <RoleBasedRoute requiredRole="employee" fallbackPath="/employee/unauthorized">
          <EmployeeAnnouncements language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/employee/communication/documents" element={
        <RoleBasedRoute requiredRole="employee" fallbackPath="/employee/unauthorized">
          <EmployeeDocuments language={language} />
        </RoleBasedRoute>
      } />
      <Route path="/employee/communication/meetings" element={
        <RoleBasedRoute requiredRole="employee" fallbackPath="/employee/unauthorized">
          <Meetings language={language} />
        </RoleBasedRoute>
      } />

      {/* Unauthorized Access */}
      <Route path="/employee/unauthorized" element={<Unauthorized language={language} />} />

      {/* Catch all - redirect to employee dashboard */}
      <Route path="*" element={<Navigate to="/employee/dashboard" replace />} />
    </Routes>
  )
}
