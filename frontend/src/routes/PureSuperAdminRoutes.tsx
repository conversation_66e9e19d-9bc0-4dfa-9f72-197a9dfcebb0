import React from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { LazyErrorBoundary } from '../utils/lazyLoad'
import RoleBasedRoute from '../components/RoleBasedRoute'

// Import only system administration components
import {
  PureSuperAdminDashboard,
  SuperAdminSystemSettings,
  SuperAdminSecurityCenter,
  AIManagement,
  AdvancedAnalytics,
  ComplianceCenter,
  UserManagement,
  Unauthorized
} from './lazyRoutes'

interface PureSuperAdminRoutesProps {
  language: 'ar' | 'en'
}

export default function PureSuperAdminRoutes({ language }: PureSuperAdminRoutesProps): void {
  return (
    <LazyErrorBoundary>
      <Routes>
        {/* SUPERADMIN Dashboard - System Overview Only */}
        <Route path="/admin/dashboard" element={
          <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
            <PureSuperAdminDashboard language={language} />
          </RoleBasedRoute>
        } />

        {/* Core System Administration */}
        <Route path="/admin/system" element={
          <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
            <SuperAdminSystemSettings language={language} />
          </RoleBasedRoute>
        } />

        <Route path="/admin/security" element={
          <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
            <SuperAdminSecurityCenter language={language} />
          </RoleBasedRoute>
        } />

        <Route path="/admin/ai" element={
          <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
            <AIManagement language={language} />
          </RoleBasedRoute>
        } />

        <Route path="/admin/analytics" element={
          <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
            <AdvancedAnalytics language={language} />
          </RoleBasedRoute>
        } />

        <Route path="/admin/compliance" element={
          <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
            <ComplianceCenter language={language} />
          </RoleBasedRoute>
        } />

        {/* User & Role Management (System Level) */}
        <Route path="/admin/users" element={
          <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
            <UserManagement language={language} />
          </RoleBasedRoute>
        } />
        {/* ROUTE FIX: Add singular /admin/user route for compatibility */}
        <Route path="/admin/user" element={
          <RoleBasedRoute requiredRole="super_admin" fallbackPath="/admin/unauthorized">
            <UserManagement language={language} />
          </RoleBasedRoute>
        } />

        {/* Unauthorized Access */}
        <Route path="/admin/unauthorized" element={<Unauthorized language={language} />} />

        {/* Redirect all other routes to dashboard */}
        <Route path="*" element={<Navigate to="/admin/dashboard" replace />} />
      </Routes>
    </LazyErrorBoundary>
  )
}
