/**
 * Lazy-loaded Routes Configuration
 * Code-split routes for better performance
 */

import { createLazyComponent, createRetryableLazyComponent } from '../utils/lazyLoad'

// Public Pages (loaded immediately)
export { default as Home } from '../pages/Home'
export { default as Login } from '../pages/Login'
export { default as HowItWorks } from '../pages/HowItWorks'

// Core Pages (with retry mechanism for critical pages)
export const Dashboard = createRetryableLazyComponent(
  ( as any) => import('../pages/Dashboard' as any),
  3
)

export const Unauthorized = createLazyComponent(
  ( as any) => import('../pages/Unauthorized' as any),
  'جاري تحميل صفحة عدم التصريح...'
)

// Admin Pages
export const UserManagement = createLazyComponent(
  ( as any) => import('../pages/admin/UserManagement' as any),
  'جاري تحميل إدارة المستخدمين...'
)

// Dashboard Pages
export const SuperAdminDashboard = createLazyComponent(
  ( as any) => import('../pages/dashboards/SuperAdminDashboard' as any),
  'جاري تحميل لوحة تحكم المدير العام...'
)

export const AdminDashboard = createLazyComponent(
  ( as any) => import('../pages/dashboards/AdminDashboard' as any),
  'جاري تحميل لوحة تحكم المدير...'
)

// Temporarily disabled due to syntax error
export const HRManagerDashboard = createLazyComponent(
  ( as any) => import('../pages/dashboards/AdminDashboard' as any),
  'جاري تحميل لوحة تحكم مدير الموارد البشرية...'
)

export const FinanceManagerDashboard = createLazyComponent(
  ( as any) => import('../pages/dashboards/FinanceManagerDashboard' as any),
  'جاري تحميل لوحة تحكم مدير المالية...'
)

export const SalesManagerDashboard = createLazyComponent(
  ( as any) => import('../pages/dashboards/SalesManagerDashboard' as any),
  'جاري تحميل لوحة تحكم مدير المبيعات...'
)

export const DepartmentManagerDashboard = createLazyComponent(
  ( as any) => import('../pages/dashboards/DepartmentManagerDashboard' as any),
  'جاري تحميل لوحة تحكم مدير القسم...'
)

export const EmployeeDashboard = createLazyComponent(
  ( as any) => import('../pages/dashboards/EmployeeDashboard' as any),
  'جاري تحميل لوحة تحكم الموظف...'
)

// Core Management Pages
export const Employees = createLazyComponent(
  ( as any) => import('../pages/Employees' as any),
  'جاري تحميل صفحة الموظفين...'
)

export const Departments = createLazyComponent(
  ( as any) => import('../pages/Departments' as any),
  'جاري تحميل صفحة الأقسام...'
)

export const Reports = createLazyComponent(
  ( as any) => import('../pages/Reports' as any),
  'جاري تحميل صفحة التقارير...'
)

export const Settings = createLazyComponent(
  ( as any) => import('../pages/Settings' as any),
  'جاري تحميل صفحة الإعدادات...'
)

// HR Pages
export const Attendance = createLazyComponent(
  ( as any) => import('../pages/hr/Attendance' as any),
  'جاري تحميل صفحة الحضور والانصراف...'
)

export const LeaveManagement = createLazyComponent(
  ( as any) => import('../pages/hr/LeaveManagement' as any),
  'جاري تحميل صفحة إدارة الإجازات...'
)

export const Payroll = createLazyComponent(
  ( as any) => import('../pages/hr/Payroll' as any),
  'جاري تحميل صفحة كشوف المرتبات...'
)

export const Performance = createLazyComponent(
  ( as any) => import('../pages/hr/Performance' as any),
  'جاري تحميل صفحة تقييم الأداء...'
)

// Sales Pages
export const Sales = createLazyComponent(
  ( as any) => import('../pages/sales/Sales' as any),
  'جاري تحميل صفحة المبيعات...'
)

export const SalesOrders = createLazyComponent(
  ( as any) => import('../pages/sales/SalesOrders' as any),
  'جاري تحميل صفحة أوامر المبيعات...'
)

export const Quotations = createLazyComponent(
  ( as any) => import('../pages/sales/Quotations' as any),
  'جاري تحميل صفحة عروض الأسعار...'
)

export const SalesPipeline = createLazyComponent(
  ( as any) => import('../pages/sales/SalesPipeline' as any),
  'جاري تحميل صفحة مسار المبيعات...'
)

// Finance Pages
export const FinanceBudgets = createLazyComponent(
  ( as any) => import('../pages/finance-specific/FinanceBudgets' as any),
  'جاري تحميل صفحة الميزانيات...'
)

export const FinanceReports = createLazyComponent(
  ( as any) => import('../pages/finance-specific/FinanceReports' as any),
  'جاري تحميل صفحة التقارير المالية...'
)

export const Expenses = createLazyComponent(
  ( as any) => import('../pages/finance/Expenses' as any),
  'جاري تحميل صفحة المصروفات...'
)

export const GeneralLedger = createLazyComponent(
  ( as any) => import('../pages/finance/GeneralLedger' as any),
  'جاري تحميل دليل الحسابات...'
)

export const Vendors = createLazyComponent(
  ( as any) => import('../pages/finance/Vendors' as any),
  'جاري تحميل صفحة الموردين...'
)

export const VendorInvoices = createLazyComponent(
  ( as any) => import('../pages/finance/VendorInvoices' as any),
  'جاري تحميل فواتير الموردين...'
)

export const Payments = createLazyComponent(
  ( as any) => import('../pages/finance/Payments' as any),
  'جاري تحميل صفحة المدفوعات...'
)

export const AgingReports = createLazyComponent(
  ( as any) => import('../pages/finance/AgingReports' as any),
  'جاري تحميل تقارير الأعمار...'
)

export const CustomerInvoices = createLazyComponent(
  ( as any) => import('../pages/finance/CustomerInvoices' as any),
  'جاري تحميل فواتير العملاء...'
)

export const Collections = createLazyComponent(
  ( as any) => import('../pages/finance/Collections' as any),
  'جاري تحميل صفحة التحصيلات...'
)

export const FinancialReportsDashboard = createLazyComponent(
  ( as any) => import('../pages/finance/FinancialReportsDashboard' as any),
  'جاري تحميل لوحة التقارير المالية...'
)

export const ProfitLossReport = createLazyComponent(
  ( as any) => import('../pages/finance/reports/ProfitLossReport' as any),
  'جاري تحميل قائمة الدخل...'
)

export const BalanceSheetReport = createLazyComponent(
  ( as any) => import('../pages/finance/reports/BalanceSheetReport' as any),
  'جاري تحميل الميزانية العمومية...'
)

export const CashFlowReport = createLazyComponent(
  ( as any) => import('../pages/finance/reports/CashFlowReport' as any),
  'جاري تحميل قائمة التدفقات النقدية...'
)

export const CurrencyManagement = createLazyComponent(
  ( as any) => import('../pages/finance/CurrencyManagement' as any),
  'جاري تحميل إدارة العملات...'
)

export const ExchangeRateDashboard = createLazyComponent(
  ( as any) => import('../pages/finance/ExchangeRateDashboard' as any),
  'جاري تحميل لوحة أسعار الصرف...'
)

export const AssetDashboard = createLazyComponent(
  ( as any) => import('../pages/finance/AssetDashboard' as any),
  'جاري تحميل لوحة الأصول...'
)

export const AssetManagement = createLazyComponent(
  ( as any) => import('../pages/finance/AssetManagement' as any),
  'جاري تحميل إدارة الأصول...'
)

export const MaintenanceManagement = createLazyComponent(
  ( as any) => import('../pages/finance/MaintenanceManagement' as any),
  'جاري تحميل إدارة الصيانة...'
)

export const ExecutiveDashboard = createLazyComponent(
  ( as any) => import('../pages/analytics/ExecutiveDashboard' as any),
  'جاري تحميل لوحة القيادة التنفيذية...'
)

export const KPIManagement = createLazyComponent(
  ( as any) => import('../pages/analytics/ModernKPIManagement' as any),
  'جاري تحميل إدارة مؤشرات الأداء...'
)

export const ReportsManagement = createLazyComponent(
  ( as any) => import('../pages/analytics/ReportsManagement' as any),
  'جاري تحميل إدارة التقارير...'
)

export const APIManagement = createLazyComponent(
  ( as any) => import('../pages/integrations/APIManagement' as any),
  'جاري تحميل إدارة API...'
)

export const ExternalServices = createLazyComponent(
  ( as any) => import('../pages/integrations/ExternalServices' as any),
  'جاري تحميل الخدمات الخارجية...'
)

export const WebhookManagement = createLazyComponent(
  ( as any) => import('../pages/integrations/WebhookManagement' as any),
  'جاري تحميل إدارة الويب هوك...'
)

export const SecurityDashboard = createLazyComponent(
  ( as any) => import('../pages/security/SecurityDashboard' as any),
  'جاري تحميل لوحة تحكم الأمان...'
)

export const SecurityIncidents = createLazyComponent(
  ( as any) => import('../pages/security/SecurityIncidents' as any),
  'جاري تحميل الحوادث الأمنية...'
)

export const ComplianceManagement = createLazyComponent(
  ( as any) => import('../pages/security/ComplianceManagement' as any),
  'جاري تحميل إدارة الامتثال...'
)

// Project Management Pages
export const Projects = createLazyComponent(
  ( as any) => import('../pages/projects/Projects' as any),
  'جاري تحميل صفحة المشاريع...'
)

export const Tasks = createLazyComponent(
  ( as any) => import('../pages/projects/Tasks' as any),
  'جاري تحميل صفحة المهام...'
)

export const ProjectReports = createLazyComponent(
  ( as any) => import('../pages/projects/ProjectReports' as any),
  'جاري تحميل صفحة تقارير المشاريع...'
)

// Asset Management Pages
export const Assets = createLazyComponent(
  ( as any) => import('../pages/assets/Assets' as any),
  'جاري تحميل صفحة الأصول...'
)

export const Suppliers = createLazyComponent(
  ( as any) => import('../pages/assets/Suppliers' as any),
  'جاري تحميل صفحة الموردين...'
)

export const PurchaseOrders = createLazyComponent(
  ( as any) => import('../pages/assets/PurchaseOrders' as any),
  'جاري تحميل صفحة أوامر الشراء...'
)

// Communication Pages
export const Messages = createLazyComponent(
  ( as any) => import('../pages/communication/Messages' as any),
  'جاري تحميل صفحة الرسائل...'
)

export const Announcements = createLazyComponent(
  ( as any) => import('../pages/communication/Announcements' as any),
  'جاري تحميل صفحة الإعلانات...'
)

export const Documents = createLazyComponent(
  ( as any) => import('../pages/communication/Documents' as any),
  'جاري تحميل صفحة المستندات...'
)

export const Meetings = createLazyComponent(
  ( as any) => import('../pages/communication/Meetings' as any),
  'جاري تحميل صفحة الاجتماعات...'
)

// Personal Pages
export const PersonalProfile = createLazyComponent(
  ( as any) => import('../pages/personal/PersonalProfile' as any),
  'جاري تحميل الملف الشخصي...'
)

export const PersonalMessages = createLazyComponent(
  ( as any) => import('../pages/personal/PersonalMessages' as any),
  'جاري تحميل الرسائل الشخصية...'
)

export const PersonalCalendar = createLazyComponent(
  ( as any) => import('../pages/personal/PersonalCalendar' as any),
  'جاري تحميل التقويم الشخصي...'
)

// Employee Specific Pages
export const EmployeeProfile = createLazyComponent(
  ( as any) => import('../pages/employee-specific/EmployeeProfile' as any),
  'جاري تحميل ملف الموظف...'
)

export const EmployeeLeave = createLazyComponent(
  ( as any) => import('../pages/employee-specific/EmployeeLeave' as any),
  'جاري تحميل طلبات الإجازة...'
)

export const EmployeeTasks = createLazyComponent(
  ( as any) => import('../pages/employee-specific/EmployeeTasks' as any),
  'جاري تحميل مهام الموظف...'
)

// Analytics Pages
export const AnalyticsComponent = createLazyComponent(
  ( as any) => import('../components/analytics/AdvancedAnalytics' as any),
  'جاري تحميل التحليلات المتقدمة...'
)

// Business Intelligence (Now handled by HierarchicalKPIDashboard)
// export const BusinessIntelligence = createLazyComponent(
//   ( as any) => import('../pages/analytics/BusinessIntelligence' as any),
//   'جاري تحميل ذكاء الأعمال...'
// )

// Other Pages
export const Calendar = createLazyComponent(
  ( as any) => import('../pages/calendar/Calendar' as any),
  'جاري تحميل التقويم...'
)

export const Customers = createLazyComponent(
  ( as any) => import('../pages/crm/Customers' as any),
  'جاري تحميل صفحة العملاء...'
)

export const Products = createLazyComponent(
  ( as any) => import('../pages/products/Products' as any),
  'جاري تحميل صفحة المنتجات...'
)

export const Inventory = createLazyComponent(
  ( as any) => import('../pages/inventory/Inventory' as any),
  'جاري تحميل صفحة المخزون...'
)

// Vendor Management
export const VendorManagement = createLazyComponent(
  ( as any) => import('../pages/vendors/VendorManagement' as any),
  'جاري تحميل صفحة إدارة الموردين...'
)

// Advanced Features
export const WorkflowAutomation = createLazyComponent(
  ( as any) => import('../pages/workflow/WorkflowManagement' as any),
  'جاري تحميل أتمتة سير العمل...'
)

export const ReportGenerator = createLazyComponent(
  ( as any) => import('../pages/reports/ReportGenerator' as any),
  'جاري تحميل مولد التقارير...'
)

export const AdvancedDashboard = createLazyComponent(
  ( as any) => import('../pages/dashboard/AdvancedDashboard' as any),
  'جاري تحميل لوحة التحكم المتقدمة...'
)

// Removed TestingDashboard and TestEnhancements - cleaned up test pages

// KPI Pages (Updated to use HierarchicalKPIDashboard)
export const KPIDashboard = createRetryableLazyComponent(
  ( as any) => import('../components/kpi/HierarchicalKPIDashboard' as any),
  3,
  'جاري تحميل لوحة مؤشرات الأداء...'
)



// All KPI dashboards now use HierarchicalKPIDashboard with different configurations
// Removed duplicate dashboard components - use KPIDashboard with dashboardType prop

// Customer Service Pages
export const SupportDashboard = createLazyComponent(
  ( as any) => import('../pages/CustomerService/SupportDashboard' as any),
  'جاري تحميل لوحة خدمة العملاء...'
)

export const TicketManagement = createLazyComponent(
  ( as any) => import('../pages/CustomerService/TicketManagement' as any),
  'جاري تحميل إدارة التذاكر...'
)

export const KnowledgeBase = createLazyComponent(
  ( as any) => import('../pages/CustomerService/KnowledgeBase' as any),
  'جاري تحميل قاعدة المعرفة...'
)

export const LiveChat = createLazyComponent(
  ( as any) => import('../pages/CustomerService/LiveChatDashboard' as any),
  'جاري تحميل لوحة الدردشة المباشرة...'
)

export const LiveChatManagement = createLazyComponent(
  ( as any) => import('../pages/CustomerService/LiveChatManagement' as any),
  'جاري تحميل إدارة الدردشة المباشرة...'
)

export const CustomerFeedback = createLazyComponent(
  ( as any) => import('../pages/CustomerService/CustomerFeedback' as any),
  'جاري تحميل تقييمات العملاء...'
)

export const AgentPerformance = createLazyComponent(
  ( as any) => import('../pages/CustomerService/AgentPerformance' as any),
  'جاري تحميل أداء وكلاء الدعم...'
)

export const AIChatAssistant = createLazyComponent(
  ( as any) => import('../pages/CustomerService/AIChatAssistant' as any),
  'جاري تحميل مساعد الذكاء الاصطناعي...'
)

export const AutomationDashboard = createLazyComponent(
  ( as any) => import('../pages/CustomerService/AutomationDashboard' as any),
  'جاري تحميل لوحة الأتمتة...'
)

export const CustomerSupportHub = createLazyComponent(
  ( as any) => import('../pages/CustomerService/CustomerSupportHub' as any),
  'جاري تحميل مركز دعم العملاء...'
)

// Super Admin Enhanced Features
export const SystemAdministration = createLazyComponent(
  ( as any) => import('../pages/admin/SystemAdministration' as any),
  'جاري تحميل إدارة النظام...'
)

export const SecurityCenter = createLazyComponent(
  ( as any) => import('../pages/admin/SecurityCenter' as any),
  'جاري تحميل مركز الأمان...'
)

export const AIManagement = createLazyComponent(
  ( as any) => import('../pages/admin/AIManagement' as any),
  'جاري تحميل إدارة الذكاء الاصطناعي...'
)

export const AdvancedAnalytics = createLazyComponent(
  ( as any) => import('../pages/admin/AdvancedAnalytics' as any),
  'جاري تحميل التحليلات المتقدمة...'
)

export const ComplianceCenter = createLazyComponent(
  ( as any) => import('../pages/admin/ComplianceCenter' as any),
  'جاري تحميل مركز الامتثال...'
)

export const SuperAdminSystemSettings = createLazyComponent(
  ( as any) => import('../pages/admin/SuperAdminSystemSettings' as any),
  'جاري تحميل إعدادات النظام المتقدمة...'
)

export const SuperAdminSecurityCenter = createLazyComponent(
  ( as any) => import('../pages/admin/SuperAdminSecurityCenter' as any),
  'جاري تحميل مركز الأمان المتقدم...'
)

export const PureSuperAdminDashboard = createLazyComponent(
  ( as any) => import('../pages/admin/PureSuperAdminDashboard' as any),
  'جاري تحميل لوحة تحكم مدير النظام...'
)

// Note: PureSuperAdminRoutes is imported directly in RoleBasedRouter, not as a lazy component

// Route preloading utilities
export const preloadRoutes = {
  dashboard: () => import('../pages/Dashboard' as any),
  employees: () => import('../pages/Employees' as any),
  departments: () => import('../pages/Departments' as any),
  reports: () => import('../pages/Reports' as any),
  settings: () => import('../pages/Settings' as any),
}

// Preload critical routes
export const preloadCriticalRoutes = (): void => {
  // Preload dashboard and core pages
  (preloadRoutes as any).dashboard( as any)
  (preloadRoutes as any).employees( as any)
  (preloadRoutes as any).departments( as any)
}
