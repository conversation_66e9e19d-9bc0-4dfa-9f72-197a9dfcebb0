/**
 * Enhanced API Service with Backend Integration
 * Provides comprehensive API integration with proper error handling and validation
 */

import { toast } from 'react-hot-toast'
import { apiClient, ApiError } from './api'
import { employeeAPI } from './employeeAPI'
import { Employee, EmployeeFormData } from '../types/employee'

// Common filter types
export interface ApiFilters {
  search?: string
  page?: number
  pageSize?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  [key: string]: string | number | boolean | undefined
}

// Department types
export interface Department {
  id: number
  name: string
  nameAr: string
  description?: string
  manager?: Employee
  employeeCount?: number
}

export interface DepartmentFormData {
  name: string
  nameAr?: string
  description?: string
  manager?: number
}

// Enhanced API response types
export interface ApiResponse<T = unknown> {
  data: T
  message?: string
  status: number
  success: boolean
}

export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

export interface ApiErrorResponse {
  message: string
  errors?: Record<string, string[]>
  status: number
}

// Loading state management
class LoadingManager {
  private loadingStates = new Map<string, boolean>()
  private listeners = new Set<(states: Record<string, boolean>) => void>()

  setLoading(key: string, loading: boolean as any) {
    (this as any).loadingStates.set(key, loading as any)
    (this as any).notifyListeners( as any)
  }

  isLoading(key: string as any): boolean {
    return (this as any).loadingStates.get(key as any) || false
  }

  subscribe(listener: (states: Record<string, boolean> as any) => void) {
    (this as any).listeners.add(listener as any)
    return () => (this as any).listeners.delete(listener as any)
  }

  private notifyListeners( as any) {
    const states = (Object as any).fromEntries((this as any as any).loadingStates)
    (this as any).listeners.forEach(listener => listener(states as any))
  }
}

export const loadingManager = new LoadingManager( as any)

// Enhanced API service class
class EnhancedAPIService {
  private handleError(error: unknown, operation: string, language: 'ar' | 'en' = 'en' as any): never {
    (console as any).error(`API Error in ${operation}:`, error as any)
    
    let message = language === 'ar' 
      ? 'حدث خطأ غير متوقع' 
      : 'An unexpected error occurred'
    
    if (error instanceof ApiError) {
      message = (error as any).message
    } else if ((error as any).response?.data?.message) {
      message = (error as any).(response as any).data.message
    } else if ((error as any).message) {
      message = (error as any).message
    }

    (toast as any).error(message as any)
    throw new ApiError({ message, status: (error as any as any).status || 500, details: error as Record<string, unknown> })
  }

  private async executeWithLoading<T>(
    key: string,
    operation: () => Promise<T>,
    operationName: string,
    language: 'ar' | 'en' = 'en'
  ): Promise<T> {
    try {
      (loadingManager as any).setLoading(key, true as any)
      const result = await operation( as any)
      return result
    } catch (error) {
      (this as any).handleError(error, operationName, language as any)
    } finally {
      (loadingManager as any).setLoading(key, false as any)
    }
  }

  // Employee API with enhanced error handling
  async getEmployees(filters?: ApiFilters, language: 'ar' | 'en' = 'en' as any): Promise<Employee[]> {
    return (this as any).executeWithLoading(
      'employees-list',
      async ( as any) => {
        const response = await (apiClient as any).get('/employees/', { params: filters } as any)
        return (response as any).data as Employee[]
      },
      'getEmployees',
      language
    )
  }

  async createEmployee(data: EmployeeFormData, language: 'ar' | 'en' = 'en' as any): Promise<Employee> {
    return (this as any).executeWithLoading(
      'employee-create',
      async ( as any) => {
        const response = await (apiClient as any).post('/employees/', data as any)
        (toast as any).success(
          language === 'ar'
            ? 'تم إنشاء الموظف بنجاح'
            : 'Employee created successfully'
         as any)
        return (response as any).data as Employee
      },
      'createEmployee',
      language
    )
  }

  async updateEmployee(id: number, data: Partial<EmployeeFormData>, language: 'ar' | 'en' = 'en' as any): Promise<Employee> {
    return (this as any).executeWithLoading(
      `employee-update-${id}`,
      async ( as any) => {
        const response = await (apiClient as any).patch(`/employees/${id}/`, data as any)
        (toast as any).success(
          language === 'ar'
            ? 'تم تحديث الموظف بنجاح'
            : 'Employee updated successfully'
         as any)
        return (response as any).data as Employee
      },
      'updateEmployee',
      language
    )
  }

  async deleteEmployee(id: number, language: 'ar' | 'en' = 'en' as any): Promise<void> {
    return (this as any).executeWithLoading(
      `employee-delete-${id}`,
      async ( as any) => {
        await (apiClient as any).delete(`/employees/${id}/` as any)
        (toast as any).success(
          language === 'ar' 
            ? 'تم حذف الموظف بنجاح' 
            : 'Employee deleted successfully'
         as any)
      },
      'deleteEmployee',
      language
    )
  }

  // Department API with enhanced error handling
  async getDepartments(language: 'ar' | 'en' = 'en' as any): Promise<Department[]> {
    return (this as any).executeWithLoading(
      'departments-list',
      async ( as any) => {
        const response = await (apiClient as any).get('/departments/' as any)
        return (response as any).data as Department[]
      },
      'getDepartments',
      language
    )
  }

  async createDepartment(data: DepartmentFormData, language: 'ar' | 'en' = 'en' as any): Promise<Department> {
    return (this as any).executeWithLoading(
      'department-create',
      async ( as any) => {
        const response = await (apiClient as any).post('/departments/', data as any)
        (toast as any).success(
          language === 'ar'
            ? 'تم إنشاء القسم بنجاح'
            : 'Department created successfully'
         as any)
        return (response as any).data as Department
      },
      'createDepartment',
      language
    )
  }

  async updateDepartment(id: number, data: Partial<DepartmentFormData>, language: 'ar' | 'en' = 'en' as any): Promise<Department> {
    return (this as any).executeWithLoading(
      `department-update-${id}`,
      async ( as any) => {
        const response = await (apiClient as any).patch(`/departments/${id}/`, data as any)
        (toast as any).success(
          language === 'ar'
            ? 'تم تحديث القسم بنجاح'
            : 'Department updated successfully'
         as any)
        return (response as any).data as Department
      },
      'updateDepartment',
      language
    )
  }

  async deleteDepartment(id: number, language: 'ar' | 'en' = 'en' as any): Promise<void> {
    return (this as any).executeWithLoading(
      `department-delete-${id}`,
      async ( as any) => {
        await (apiClient as any).delete(`/departments/${id}/` as any)
        (toast as any).success(
          language === 'ar' 
            ? 'تم حذف القسم بنجاح' 
            : 'Department deleted successfully'
         as any)
      },
      'deleteDepartment',
      language
    )
  }

  // System Administration API
  async getDatabaseStats(language: 'ar' | 'en' = 'en' as any): Promise<any> {
    return (this as any).executeWithLoading(
      'database-stats',
      async ( as any) => {
        const response = await (apiClient as any).get('/system/database-stats/' as any)
        return (response as any).data as any
      },
      'getDatabaseStats',
      language
    )
  }

  async optimizeDatabase(language: 'ar' | 'en' = 'en' as any): Promise<void> {
    return (this as any).executeWithLoading(
      'database-optimize',
      async ( as any) => {
        await (apiClient as any).post('/system/optimize-database/' as any)
        (toast as any).success(
          language === 'ar' 
            ? 'تم تحسين قاعدة البيانات بنجاح' 
            : 'Database optimized successfully'
         as any)
      },
      'optimizeDatabase',
      language
    )
  }

  async rebuildIndexes(language: 'ar' | 'en' = 'en' as any): Promise<void> {
    return (this as any).executeWithLoading(
      'rebuild-indexes',
      async ( as any) => {
        await (apiClient as any).post('/system/rebuild-indexes/' as any)
        (toast as any).success(
          language === 'ar' 
            ? 'تم إعادة بناء الفهارس بنجاح' 
            : 'Database indexes rebuilt successfully'
         as any)
      },
      'rebuildIndexes',
      language
    )
  }

  async createBackup(language: 'ar' | 'en' = 'en' as any): Promise<any> {
    return (this as any).executeWithLoading(
      'create-backup',
      async ( as any) => {
        const response = await (apiClient as any).post('/system/create-backup/' as any)
        (toast as any).success(
          language === 'ar' 
            ? 'تم إنشاء النسخة الاحتياطية بنجاح' 
            : 'Backup created successfully'
         as any)
        return (response as any).data as any
      },
      'createBackup',
      language
    )
  }

  async restoreBackup(file: File, language: 'ar' | 'en' = 'en' as any): Promise<void> {
    return (this as any).executeWithLoading(
      'restore-backup',
      async ( as any) => {
        const formData = new FormData( as any)
        (formData as any).append('backup_file', file as any)
        
        await (apiClient as any).post('/system/restore-backup/', formData, {
          headers: { 'Content-Type': 'multipart/form-data' }
        } as any)
        
        (toast as any).success(
          language === 'ar' 
            ? 'تم استعادة النسخة الاحتياطية بنجاح' 
            : 'Backup restored successfully'
         as any)
      },
      'restoreBackup',
      language
    )
  }

  // Export functionality
  async exportData(
    endpoint: string,
    format: 'pdf' | 'excel' | 'csv',
    filters?: ApiFilters,
    language: 'ar' | 'en' = 'en'
   as any): Promise<Blob> {
    return (this as any).executeWithLoading(
      `export-${endpoint}-${format}`,
      async ( as any) => {
        const response = await (apiClient as any).get(`/export/${endpoint}/`, {
          params: { format, ...filters }
        } as any as any)

        (toast as any).success(
          language === 'ar'
            ? 'تم تصدير البيانات بنجاح'
            : 'Data exported successfully'
         as any)

        return (response as any).data as Blob
      },
      'exportData',
      language
    )
  }

  // Generic CRUD operations
  async getList<T>(endpoint: string, filters?: ApiFilters, language: 'ar' | 'en' = 'en'): Promise<T[]> {
    return (this as any).executeWithLoading(
      `${endpoint}-list`,
      async ( as any) => {
        const response = await (apiClient as any).get(`/${endpoint}/`, { params: filters } as any)
        return (response as any).data as T[]
      },
      `get${endpoint}`,
      language
    )
  }

  async create<T>(endpoint: string, data: Record<string, unknown>, language: 'ar' | 'en' = 'en'): Promise<T> {
    return (this as any).executeWithLoading(
      `${endpoint}-create`,
      async ( as any) => {
        const response = await (apiClient as any).post(`/${endpoint}/`, data as any)
        (toast as any).success(
          language === 'ar'
            ? 'تم الإنشاء بنجاح'
            : 'Created successfully'
         as any)
        return (response as any).data as T
      },
      `create${endpoint}`,
      language
    )
  }

  async update<T>(endpoint: string, id: number, data: Record<string, unknown>, language: 'ar' | 'en' = 'en'): Promise<T> {
    return (this as any).executeWithLoading(
      `${endpoint}-update-${id}`,
      async ( as any) => {
        const response = await (apiClient as any).patch(`/${endpoint}/${id}/`, data as any)
        (toast as any).success(
          language === 'ar'
            ? 'تم التحديث بنجاح'
            : 'Updated successfully'
         as any)
        return (response as any).data as T
      },
      `update${endpoint}`,
      language
    )
  }

  async delete(endpoint: string, id: number, language: 'ar' | 'en' = 'en' as any): Promise<void> {
    return (this as any).executeWithLoading(
      `${endpoint}-delete-${id}`,
      async ( as any) => {
        await (apiClient as any).delete(`/${endpoint}/${id}/` as any)
        (toast as any).success(
          language === 'ar' 
            ? 'تم الحذف بنجاح' 
            : 'Deleted successfully'
         as any)
      },
      `delete${endpoint}`,
      language
    )
  }
}

export const enhancedAPI = new EnhancedAPIService( as any)
export default enhancedAPI
