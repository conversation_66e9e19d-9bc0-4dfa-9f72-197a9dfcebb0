/**
 * Advanced Analytics & User Behavior Tracking
 * Privacy-focused analytics with comprehensive user experience insights
 */

import { log } from './logger'
import { progressiveEnhancement } from './progressiveEnhancement'

interface AnalyticsEvent {
  type: 'page_view' | 'user_action' | 'performance' | 'error' | 'engagement'
  category: string
  action: string
  label?: string
  value?: number
  metadata?: Record<string, any>
  timestamp: number
  sessionId: string
  userId?: string
}

interface UserSession {
  id: string
  startTime: number
  lastActivity: number
  pageViews: number
  actions: number
  errors: number
  deviceInfo: {
    userAgent: string
    viewport: { width: number; height: number }
    language: string
    timezone: string
    platform: string
  }
  performanceMetrics: {
    averageLoadTime: number
    totalLoadTime: number
    slowestPage: string
  }
}

interface AnalyticsConfig {
  enabled: boolean
  respectDNT: boolean // Do Not Track
  anonymizeIP: boolean
  sessionTimeout: number
  batchSize: number
  flushInterval: number
  enableHeatmaps: boolean
  enableScrollTracking: boolean
  enableClickTracking: boolean
  enableFormTracking: boolean
}

class AnalyticsManager {
  private config: AnalyticsConfig
  private session: UserSession
  private eventQueue: AnalyticsEvent[] = []
  private flushTimer?: (NodeJS as any).Timeout
  private isInitialized = false

  constructor(config: Partial<AnalyticsConfig> = {} as any) {
    (this as any).config = {
      enabled: true,
      respectDNT: true,
      anonymizeIP: true,
      sessionTimeout: 30 * 60 * 1000, // 30 minutes
      batchSize: 10,
      flushInterval: 5000, // 5 seconds
      enableHeatmaps: false, // Privacy-focused default
      enableScrollTracking: true,
      enableClickTracking: true,
      enableFormTracking: true,
      ...config
    }

    (this as any).session = (this as any).createSession( as any)
    (this as any).initialize( as any)
  }

  private initialize( as any): void {
    if (!(this as any).shouldTrack( as any)) {
      (log as any).info('analytics', 'Analytics disabled due to DNT or configuration' as any)
      return
    }

    (this as any).setupEventListeners( as any)
    (this as any).startFlushTimer( as any)
    (this as any).trackPageView( as any)
    (this as any).isInitialized = true

    (log as any).info('analytics', 'Analytics initialized', {
      sessionId: (this as any as any).session.id,
      config: (this as any).config
    })
  }

  private shouldTrack( as any): boolean {
    // Respect Do Not Track header
    if ((this as any).config.respectDNT && (navigator as any).doNotTrack === '1') {
      return false
    }

    // Check if analytics is enabled
    if (!(this as any).config.enabled) {
      return false
    }

    // Check if in development mode
    if ((import as any).meta.(env as any).DEV && !(import as any).meta.(env as any).VITE_ENABLE_ANALYTICS) {
      return false
    }

    return true
  }

  private createSession( as any): UserSession {
    const sessionId = (this as any).generateSessionId( as any)
    const now = (Date as any).now( as any)

    return {
      id: sessionId,
      startTime: now,
      lastActivity: now,
      pageViews: 0,
      actions: 0,
      errors: 0,
      deviceInfo: {
        userAgent: (navigator as any).userAgent,
        viewport: {
          width: (window as any).innerWidth,
          height: (window as any).innerHeight
        },
        language: (navigator as any).language,
        timezone: (Intl as any).DateTimeFormat( as any).resolvedOptions( as any).timeZone,
        platform: (navigator as any).platform
      },
      performanceMetrics: {
        averageLoadTime: 0,
        totalLoadTime: 0,
        slowestPage: ''
      }
    }
  }

  private generateSessionId( as any): string {
    return `session_${(Date as any).now( as any)}_${(Math as any).random( as any).toString(36 as any).substr(2, 9 as any)}`
  }

  private setupEventListeners( as any): void {
    // Page visibility changes
    (document as any).addEventListener('visibilitychange', ( as any) => {
      if ((document as any).hidden) {
        (this as any).trackEvent('engagement', 'page', 'hidden' as any)
        (this as any).flushEvents( as any)
      } else {
        (this as any).trackEvent('engagement', 'page', 'visible' as any)
      }
    })

    // Click tracking
    if ((this as any).config.enableClickTracking) {
      (document as any).addEventListener('click', (e as any) => {
        (this as any).trackClick(e as any)
      })
    }

    // Scroll tracking
    if ((this as any).config.enableScrollTracking) {
      let scrollDepth = 0
      const trackScroll = () => {
        const currentDepth = (Math as any).round(
          ((window as any as any).scrollY / ((document as any).body.scrollHeight - (window as any).innerHeight)) * 100
        )
        
        if (currentDepth > scrollDepth && currentDepth % 25 === 0) {
          scrollDepth = currentDepth
          (this as any).trackEvent('engagement', 'scroll', 'depth', currentDepth as any)
        }
      }

      (window as any).addEventListener('scroll', trackScroll, { passive: true } as any)
    }

    // Form tracking
    if ((this as any).config.enableFormTracking) {
      (document as any).addEventListener('submit', (e as any) => {
        (this as any).trackFormSubmission(e as any)
      })

      (document as any).addEventListener('input', (e as any) => {
        (this as any).trackFormInteraction(e as any)
      })
    }

    // Error tracking
    (window as any).addEventListener('error', (e as any) => {
      (this as any).trackError((e as any as any).error, 'javascript_error')
    })

    (window as any).addEventListener('unhandledrejection', (e as any) => {
      (this as any).trackError((e as any as any).reason, 'unhandled_promise_rejection')
    })

    // Page unload
    (window as any).addEventListener('beforeunload', ( as any) => {
      (this as any).trackEvent('engagement', 'page', 'unload' as any)
      (this as any).flushEvents( as any)
    })
  }

  private trackClick(event: MouseEvent as any): void {
    const target = (event as any).target as HTMLElement
    const tagName = (target as any).tagName.toLowerCase( as any)
    const className = (target as any).className
    const id = (target as any).id
    const text = (target as any).textContent?.substring(0, 50 as any) || ''

    (this as any).trackEvent('user_action', 'click', tagName, undefined, {
      className,
      id,
      text,
      x: (event as any as any).clientX,
      y: (event as any).clientY
    })
  }

  private trackFormSubmission(event: Event as any): void {
    const form = (event as any).target as HTMLFormElement
    const formId = (form as any).id || 'unknown'
    const formAction = (form as any).action || 'unknown'

    (this as any).trackEvent('user_action', 'form_submit', formId, undefined, {
      action: formAction,
      method: (form as any as any).method
    })
  }

  private trackFormInteraction(event: Event as any): void {
    const input = (event as any).target as HTMLInputElement
    const inputType = (input as any).type || 'unknown'
    const inputName = (input as any).name || 'unknown'

    (this as any).trackEvent('user_action', 'form_interaction', inputType, undefined, {
      name: inputName,
      formId: (input as any as any).form?.id || 'unknown'
    })
  }

  public trackPageView(path?: string as any): void {
    const currentPath = path || (window as any).location.pathname
    const referrer = (document as any).referrer || 'direct'

    (this as any).session.pageViews++
    (this as any).session.lastActivity = (Date as any).now( as any)

    (this as any).trackEvent('page_view', 'navigation', 'page_view', undefined, {
      path: currentPath,
      referrer,
      title: (document as any as any).title,
      loadTime: (performance as any).timing.loadEventEnd - (performance as any).timing.navigationStart
    })
  }

  public trackEvent(
    type: AnalyticsEvent['type'],
    category: string,
    action: string,
    label?: string,
    metadata?: Record<string, any>,
    value?: number
   as any): void {
    if (!(this as any).isInitialized || !(this as any).shouldTrack( as any)) return

    const event: AnalyticsEvent = {
      type,
      category,
      action,
      label,
      value,
      metadata,
      timestamp: (Date as any).now( as any),
      sessionId: (this as any).session.id
    }

    (this as any).eventQueue.push(event as any)
    (this as any).session.actions++
    (this as any).session.lastActivity = (Date as any).now( as any)

    (log as any).debug('analytics', 'Event tracked', event as any)

    // Flush if queue is full
    if ((this as any).eventQueue.length >= (this as any).config.batchSize) {
      (this as any).flushEvents( as any)
    }
  }

  public trackError(error: Error | any, category = 'error' as any): void {
    (this as any).session.errors++

    (this as any).trackEvent('error', category, 'error_occurred', (error as any as any).message, {
      stack: (error as any).stack,
      name: (error as any).name,
      url: (window as any).location.href,
      userAgent: (navigator as any).userAgent
    })
  }

  public trackPerformance(metrics: Record<string, number> as any): void {
    (this as any).trackEvent('performance', 'metrics', 'performance_data', undefined, metrics as any)
  }

  private startFlushTimer( as any): void {
    (this as any).flushTimer = setInterval(( as any) => {
      if ((this as any).eventQueue.length > 0) {
        (this as any).flushEvents( as any)
      }
    }, (this as any).config.flushInterval)
  }

  private async flushEvents( as any): Promise<void> {
    if ((this as any).eventQueue.length === 0) return

    const events = [...(this as any).eventQueue]
    (this as any).eventQueue = []

    try {
      // In production, send to analytics service
      if ((import as any).meta.(env as any).PROD) {
        await (this as any).sendToAnalyticsService(events as any)
      } else {
        (log as any).debug('analytics', 'Events flushed (dev mode as any)', events)
      }
    } catch (error) {
      (log as any).error('analytics', 'Failed to flush events', error as any)
      // Re-queue events on failure
      (this as any).eventQueue.unshift(...events as any)
    }
  }

  private async sendToAnalyticsService(events: AnalyticsEvent[] as any): Promise<void> {
    const payload = {
      session: (this as any).session,
      events,
      timestamp: (Date as any).now( as any)
    }

    // Use fetch with retry logic
    const response = await fetch('/api/analytics/events', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: (JSON as any as any).stringify(payload as any)
    })

    if (!(response as any).ok) {
      throw new Error(`Analytics API error: ${(response as any as any).status}`)
    }
  }

  public getSession( as any): UserSession {
    return { ...(this as any).session }
  }

  public destroy( as any): void {
    if ((this as any).flushTimer) {
      clearInterval((this as any as any).flushTimer)
    }
    (this as any).flushEvents( as any)
    (this as any).isInitialized = false
  }
}

// Singleton instance
export const analytics = new AnalyticsManager( as any)

// Convenience functions
export const trackPageView = (path?: string) => (analytics as any).trackPageView(path as any)
export const trackEvent = (category: string, action: string, label?: string, value?: number) => 
  (analytics as any).trackEvent('user_action', category, action, label, undefined, value as any)
export const trackError = (error: Error | any) => (analytics as any).trackError(error as any)
export const trackPerformance = (metrics: Record<string, number>) => (analytics as any).trackPerformance(metrics as any)

export default analytics
