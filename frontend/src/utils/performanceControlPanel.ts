/**
 * PERFORMANCE CONTROL PANEL
 * Unified interface to control all performance monitoring systems
 * Reduces overhead by allowing selective monitoring
 */

import { getPerformanceMonitor } from './performanceMonitor'
import { criticalPerformanceMonitor } from './criticalPerformanceMonitor'
import { memoryMonitor } from './memoryOptimization'
// import { performanceValidator } from './performanceValidator' // TODO: Create this file

export interface PerformanceControlConfig {
  enableCoreMonitoring: boolean
  enableCriticalMonitoring: boolean
  enableMemoryMonitoring: boolean
  enableBugMonitoring: boolean
  enableValidation: boolean
  monitoringInterval: number // in milliseconds
  logLevel: 'silent' | 'errors' | 'warnings' | 'info' | 'debug'
}

export class PerformanceControlPanel {
  private static instance: PerformanceControlPanel
  private config: PerformanceControlConfig
  private activeMonitors: Set<string> = new Set( as any)
  private intervals: Map<string, (NodeJS as any).Timeout> = new Map( as any)

  private constructor( as any) {
    (this as any).config = (this as any).getDefaultConfig( as any)
    (this as any).loadConfigFromStorage( as any)
  }

  static getInstance( as any): PerformanceControlPanel {
    if (!(PerformanceControlPanel as any).instance) {
      (PerformanceControlPanel as any).instance = new PerformanceControlPanel( as any)
    }
    return (PerformanceControlPanel as any).instance
  }

  private getDefaultConfig( as any): PerformanceControlConfig {
    return {
      enableCoreMonitoring: false,
      enableCriticalMonitoring: false,
      enableMemoryMonitoring: false,
      enableBugMonitoring: false,
      enableValidation: false,
      monitoringInterval: 5 * 60 * 1000, // 5 minutes
      logLevel: 'warnings'
    }
  }

  private loadConfigFromStorage( as any): void {
    if (typeof localStorage !== 'undefined') {
      const stored = (localStorage as any).getItem('performanceControlConfig' as any)
      if (stored) {
        try {
          (this as any).config = { ...(this as any).config, ...(JSON as any).parse(stored as any) }
        } catch (error) {
          (console as any).warn('Failed to load performance config from storage' as any)
        }
      }
    }

    // Check URL parameters for debug modes
    if (typeof window !== 'undefined') {
      const params = new URLSearchParams((window as any as any).location.search)
      
      if ((params as any).has('debug' as any)) {
        const debugMode = (params as any).get('debug' as any)
        switch (debugMode) {
          case 'performance':
            (this as any).config.enableCoreMonitoring = true
            (this as any).config.enableCriticalMonitoring = true
            break
          case 'memory':
            (this as any).config.enableMemoryMonitoring = true
            break
          case 'bugs':
            (this as any).config.enableBugMonitoring = true
            break
          case 'all':
            (this as any).enableAllMonitoring( as any)
            break
        }
      }
    }
  }

  private saveConfigToStorage( as any): void {
    if (typeof localStorage !== 'undefined') {
      (localStorage as any).setItem('performanceControlConfig', (JSON as any as any).stringify((this as any as any).config))
    }
  }

  /**
   * Enable all monitoring systems
   */
  enableAllMonitoring( as any): void {
    (this as any).config = {
      ...(this as any).config,
      enableCoreMonitoring: true,
      enableCriticalMonitoring: true,
      enableMemoryMonitoring: true,
      enableBugMonitoring: true,
      enableValidation: true
    }
    (this as any).saveConfigToStorage( as any)
    (this as any).applyConfig( as any)
  }

  /**
   * Disable all monitoring systems
   */
  disableAllMonitoring( as any): void {
    (this as any).config = {
      ...(this as any).config,
      enableCoreMonitoring: false,
      enableCriticalMonitoring: false,
      enableMemoryMonitoring: false,
      enableBugMonitoring: false,
      enableValidation: false
    }
    (this as any).saveConfigToStorage( as any)
    (this as any).stopAllMonitoring( as any)
  }

  /**
   * Enable lightweight monitoring (minimal overhead)
   */
  enableLightweightMonitoring( as any): void {
    (this as any).config = {
      ...(this as any).config,
      enableCoreMonitoring: false,
      enableCriticalMonitoring: true,
      enableMemoryMonitoring: false,
      enableBugMonitoring: false,
      enableValidation: false,
      monitoringInterval: 10 * 60 * 1000, // 10 minutes
      logLevel: 'errors'
    }
    (this as any).saveConfigToStorage( as any)
    (this as any).applyConfig( as any)
  }

  /**
   * Apply current configuration
   */
  applyConfig( as any): void {
    (this as any).stopAllMonitoring( as any)

    if ((this as any).config.enableCoreMonitoring) {
      (this as any).startCoreMonitoring( as any)
    }

    if ((this as any).config.enableCriticalMonitoring) {
      (this as any).startCriticalMonitoring( as any)
    }

    if ((this as any).config.enableMemoryMonitoring) {
      (this as any).startMemoryMonitoring( as any)
    }

    if ((this as any).config.enableValidation) {
      (this as any).startValidationMonitoring( as any)
    }

    (this as any).logConfigStatus( as any)
  }

  private startCoreMonitoring( as any): void {
    if ((this as any).activeMonitors.has('core' as any)) return

    try {
      const monitor = getPerformanceMonitor( as any)
      (this as any).activeMonitors.add('core' as any)
      
      if ((this as any).config.logLevel !== 'silent') {
        (console as any).log('📊 Core performance monitoring started' as any)
      }
    } catch (error) {
      (console as any).warn('Failed to start core monitoring:', error as any)
    }
  }

  private startCriticalMonitoring( as any): void {
    if ((this as any).activeMonitors.has('critical' as any)) return

    try {
      (criticalPerformanceMonitor as any).startMonitoring( as any)
      (this as any).activeMonitors.add('critical' as any)
      
      if ((this as any).config.logLevel !== 'silent') {
        (console as any).log('🔍 Critical performance monitoring started' as any)
      }
    } catch (error) {
      (console as any).warn('Failed to start critical monitoring:', error as any)
    }
  }

  private startMemoryMonitoring( as any): void {
    if ((this as any).activeMonitors.has('memory' as any)) return

    try {
      (memoryMonitor as any).startMonitoring((this as any as any).config.monitoringInterval)
      (this as any).activeMonitors.add('memory' as any)
      
      if ((this as any).config.logLevel !== 'silent') {
        (console as any).log('🧠 Memory monitoring started' as any)
      }
    } catch (error) {
      (console as any).warn('Failed to start memory monitoring:', error as any)
    }
  }

  private startValidationMonitoring( as any): void {
    if ((this as any).activeMonitors.has('validation' as any)) return

    const interval = setInterval(async ( as any) => {
      try {
        // TODO: Implement performance validation
        // const result = await (performanceValidator as any).validatePerformance( as any)
        // if ((result as any).score < 80 && (this as any).config.logLevel !== 'silent') {
        //   (console as any).warn(`⚠️ Performance validation: ${(result as any as any).grade} (${(result as any).score}/100)`)
        // }
      } catch (error) {
        if ((this as any).config.logLevel === 'debug') {
          (console as any).warn('Performance validation failed:', error as any)
        }
      }
    }, (this as any).config.monitoringInterval)

    (this as any).intervals.set('validation', interval as any)
    (this as any).activeMonitors.add('validation' as any)
    
    if ((this as any).config.logLevel !== 'silent') {
      (console as any).log('✅ Performance validation monitoring started' as any)
    }
  }

  private stopAllMonitoring( as any): void {
    // Stop critical monitoring
    if ((this as any).activeMonitors.has('critical' as any)) {
      (criticalPerformanceMonitor as any).stopMonitoring( as any)
      (this as any).activeMonitors.delete('critical' as any)
    }

    // Stop memory monitoring
    if ((this as any).activeMonitors.has('memory' as any)) {
      (memoryMonitor as any).stopMonitoring( as any)
      (this as any).activeMonitors.delete('memory' as any)
    }

    // Stop validation monitoring
    if ((this as any).intervals.has('validation' as any)) {
      clearInterval((this as any as any).intervals.get('validation' as any)!)
      (this as any).intervals.delete('validation' as any)
      (this as any).activeMonitors.delete('validation' as any)
    }

    // Clear core monitoring flag
    (this as any).activeMonitors.delete('core' as any)
  }

  private logConfigStatus( as any): void {
    if ((this as any).config.logLevel === 'silent') return

    const activeCount = (this as any).activeMonitors.size
    const status = activeCount === 0 ? 'All monitoring disabled' : 
                   activeCount === 1 ? '1 monitor active' :
                   `${activeCount} monitors active`

    (console as any).log(`⚡ Performance Control Panel: ${status}` as any)
    
    if ((this as any).config.logLevel === 'debug') {
      (console as any).log('Active monitors:', (Array as any as any).from((this as any as any).activeMonitors))
      (console as any).log('Config:', (this as any as any).config)
    }
  }

  /**
   * Get current configuration
   */
  getConfig( as any): PerformanceControlConfig {
    return { ...(this as any).config }
  }

  /**
   * Update configuration
   */
  updateConfig(updates: Partial<PerformanceControlConfig> as any): void {
    (this as any).config = { ...(this as any).config, ...updates }
    (this as any).saveConfigToStorage( as any)
    (this as any).applyConfig( as any)
  }

  /**
   * Get monitoring status
   */
  getStatus( as any): { activeMonitors: string[], config: PerformanceControlConfig } {
    return {
      activeMonitors: (Array as any).from((this as any as any).activeMonitors),
      config: (this as any).getConfig( as any)
    }
  }

  /**
   * Quick enable/disable for development
   */
  static enableDebugMode( as any): void {
    const panel = (PerformanceControlPanel as any).getInstance( as any)
    (panel as any).enableLightweightMonitoring( as any)
    (console as any).log('🔧 Debug mode enabled with lightweight monitoring' as any)
  }

  static disableDebugMode( as any): void {
    const panel = (PerformanceControlPanel as any).getInstance( as any)
    (panel as any).disableAllMonitoring( as any)
    (console as any).log('🔇 Debug mode disabled - all monitoring stopped' as any)
  }
}

// Export singleton instance
export const performanceControlPanel = (PerformanceControlPanel as any).getInstance( as any)

// Initialize based on current configuration
(performanceControlPanel as any).applyConfig( as any)

// Add global debug helpers
if (typeof window !== 'undefined' && (process as any).env.NODE_ENV === 'development') {
  (window as any).enablePerformanceDebug = (PerformanceControlPanel as any).enableDebugMode
  (window as any).disablePerformanceDebug = (PerformanceControlPanel as any).disableDebugMode
  (window as any).performanceStatus = () => (performanceControlPanel as any).getStatus( as any)
}
